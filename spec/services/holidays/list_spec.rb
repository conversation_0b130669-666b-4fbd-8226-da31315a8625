require 'rails_helper'

RSpec.describe Holidays::List, type: :service, holidays: true do

  let!(:year) { Time.zone.now.beginning_of_year.to_date }
  let!(:holiday1) { create(:holiday, :random, on_date: year + 10.days) }
  let!(:holiday2) { create(:holiday, :random, on_date: year + 1.month + 10.days) }
  let!(:holiday3) { create(:holiday, :random, on_date: year + 2.months + 10.days) }
  let!(:holiday4) { create(:holiday, :random, on_date: year + 3.months + 10.days) }
  let!(:holiday5) { create(:holiday, :random, on_date: year + 4.months + 10.days, state: 'NSW') }

  it 'lists all the holidays for the current year' do
    holidays = Holidays::List.new.call

    expect(holidays.size).to eq(5)
    expect(holidays).to include(holiday1, holiday2, holiday3, holiday4, holiday5)
  end

  it 'returns holidays within the range of the passed in from and to dates' do
    lister_options = { from_date: year + 1.month, to_date: year + 3.months }
    holidays = Holidays::List.new(options: lister_options).call

    expect(holidays).to include(holiday2, holiday3)
    expect(holidays).to_not include(holiday1, holiday4, holiday5)
  end

  context 'filter by query' do
    it 'filters the holidays by passed in holiday name' do
      lister_options = { query: holiday3.name }
      holidays = Holidays::List.new(options: lister_options).call

      expect(holidays.count).to eq(1)
      expect(holidays).to include(holiday3)
    end

    it 'filters the holidays by passed in holiday state' do
      lister_options = { query: holiday5.state }
      holidays = Holidays::List.new(options: lister_options).call

      expect(holidays.count).to eq(1)
      expect(holidays).to include(holiday5)
    end
  end

  context 'filter by state' do
    before do
      [holiday1, holiday4].each{|holiday| holiday.update_column(:state, 'VIC') }
      [holiday2, holiday5].each{|holiday| holiday.update_column(:state, 'NSW') }
      holiday3.update_column(:state, nil)
    end

    it 'filters the holidays by state (either exact or blank)' do
      lister_options = { state: 'NSW' }
      nsw_holidays = Holidays::List.new(options: lister_options).call

      expect(nsw_holidays.count).to eq(3)
      expect(nsw_holidays).to include(holiday2, holiday5, holiday3)
      expect(nsw_holidays).to_not include(holiday1, holiday4)

      lister_options = { state: 'VIC' }
      vic_holidays = Holidays::List.new(options: lister_options).call

      expect(vic_holidays.count).to eq(3)
      expect(vic_holidays).to include(holiday1, holiday4, holiday3)
      expect(vic_holidays).to_not include(holiday2, holiday5)
    end
  end

  context 'filter by public holidays only' do
    before do
      [holiday2, holiday4].each{|holiday| holiday.update_column(:push_to, nil) }
      [holiday1, holiday3, holiday5].each{|holiday| holiday.update_column(:push_to, holiday.on_date + 1.day) }
    end

    it 'filters public holidays only (push_to is present)' do
      lister_options = { public_holidays_only: true }
      nsw_holidays = Holidays::List.new(options: lister_options).call

      expect(nsw_holidays.count).to eq(3)
      expect(nsw_holidays).to include(holiday1, holiday3, holiday5)
      expect(nsw_holidays).to_not include(holiday2, holiday4)
    end
  end

  context 'default sorting' do
    it 'sorts the holidays based on on_date' do
      holidays = Holidays::List.new.call

      expect(holidays[0]).to eq(holiday1)
      expect(holidays[1]).to eq(holiday2)
      expect(holidays[2]).to eq(holiday3)
      expect(holidays[3]).to eq(holiday4)
      expect(holidays[4]).to eq(holiday5)
    end

    it 'sorts the holidays based on name if has same on_date' do
      holiday3.update_columns(name: 'A Holiday', on_date: holiday2.on_date)
      holiday2.update_column(:name, 'B Holiday')

      holidays = Holidays::List.new.call

      expect(holidays[0]).to eq(holiday1)
      expect(holidays[1]).to eq(holiday3) # holiday 3 before holiday 2
      expect(holidays[2]).to eq(holiday2)
      expect(holidays[3]).to eq(holiday4)
      expect(holidays[4]).to eq(holiday5)
    end
  end

  it 'paginates the holidays based on filter options' do
    lister_options = { page: 2, limit: 2 }
    holidays = Holidays::List.new(options: lister_options).call

    expect(holidays).to include(holiday3, holiday4)
  end

end

