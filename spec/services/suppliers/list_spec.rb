require 'rails_helper'

RSpec.describe Suppliers::List, type: :service, suppliers: true do

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier1', flags: { needs_swipe_card_access: true }) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier2', is_searchable: false, flags: { supplies_in_working_hours: true }, team_supplier: true) }
  let!(:supplier3) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier3', flags: { supplies_in_working_hours: true, provides_multi_service_point: true }, team_supplier: true) }
  let!(:supplier4) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier4', flags: { provides_multi_service_point: true }) }

  let!(:catering_category) { create(:category, :random, name: Faker::Name.name, group: 'catering-services') }
  let!(:kitchen_category) { create(:category, :random, name: Faker::Name.name, group: 'kitchen-supplies') }
  let!(:other_category) { create(:category, :random, name: Faker::Name.name, group: (Category::SUPPLIER_CATEGORIES - %w[catering-services kitchen-supplies]).sample) }

  let!(:menu_section11) { create(:menu_section, :random, categories: [catering_category], supplier_profile: supplier1) }
  let!(:menu_section12) { create(:menu_section, :random, categories: [kitchen_category], supplier_profile: supplier2) }
  let!(:menu_section13) { create(:menu_section, :random, categories: [other_category], supplier_profile: supplier3) }
  let!(:menu_section14) { create(:menu_section, :random, categories: [catering_category], supplier_profile: supplier4) }
  let!(:menu_section24) { create(:menu_section, :random, categories: [kitchen_category], supplier_profile: supplier4) }

  let!(:menu_item11) { create(:menu_item, :random, name: 'menu_item11', description: 'menu_item11_description', supplier_profile: supplier1, menu_section: menu_section11) }
  let!(:menu_item12) { create(:menu_item, :random, name: 'menu_item12', description: 'menu_item12_description', supplier_profile: supplier2, menu_section: menu_section12) }

  before do
    if [true, false].sample # explicitly set supplier category group flags
      [supplier1, supplier4].each do |supplier|
        supplier.supplier_flags.update_column(:has_catering_services, true)
      end
      [supplier2, supplier4].each do |supplier|
        supplier.supplier_flags.update_column(:has_kitchen_supplies, true)
      end
    else # cache category groups
      Suppliers::Cache::CategoryGroups.new(suppliers: [supplier1, supplier2, supplier3, supplier4]).call
    end
  end

  it 'lists all suppliers by default' do
    lister_options = {}
    suppliers = Suppliers::List.new(options: lister_options).call
    expect(suppliers).to include(supplier1, supplier2, supplier3, supplier4)
  end

  it 'only lists suppliers which are searchable' do
    lister_options = { searchable: true }
    suppliers = Suppliers::List.new(options: lister_options).call
    expect(suppliers).to include(supplier1, supplier3, supplier4)
    expect(suppliers).to_not include(supplier2)
  end

  context 'with customer restrictions' do
    let!(:customer1) { create(:customer_profile, :random) }
    let!(:customer2) { create(:customer_profile, :random) }

    # restrict supplier3 to customer1
    let!(:customer_supplier_restriction) { create(:customer_profiles_supplier_profile, customer_profile: customer1, supplier_profile: supplier3) }

    it 'only lists if supplier is visible to the supplied customer' do
      lister_options = { visible_to: customer1 }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2, supplier3, supplier4)

      lister_options = { visible_to: customer2 }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2, supplier4)
      expect(suppliers).to_not include(supplier3)
    end

    it 'doesn\'t list suppliers with restrictions if visible to is not supplied' do
      lister_options = { visible_to: nil }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2, supplier4)
      expect(suppliers).to_not include(supplier3)
    end

    it 'lists all suppliers for_cache even with restrictions' do
      lister_options = { for_cache: true, visible_to: [customer1, customer2, nil].sample } # visible_to is irrelevant
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2, supplier3, supplier4)
    end
  end

  context 'with admin restrictions' do
    before do
      [supplier1, supplier3].each{|supplier| supplier.supplier_flags.update_column(:admin_only, true) }
    end

    it 'does not return an admin only supplier for non-admins (by default)' do
      suppliers = Suppliers::List.new(options: [{}, { is_admin: false }, { is_admin: nil }].sample).call

      expect(suppliers).to_not include(supplier1, supplier3)
      expect(suppliers).to include(supplier2, supplier4)
    end

    it 'lists admin only supplier when is_admin is passed as true' do
      suppliers = Suppliers::List.new(options: { is_admin: true }).call

      expect(suppliers).to include(supplier1, supplier2, supplier3, supplier4)
    end
  end

  context 'suburb based filtering', deliverable_suburbs: true do
    let!(:suburb1) { create(:suburb, :random, state: 'NSW') }

    let!(:delivery_zone111) { create(:delivery_zone, :random, suburb: suburb1, supplier_profile: supplier1, delivery_fee: 0, radius: 0) }
    let!(:delivery_zone112) { create(:delivery_zone, :random, suburb: suburb1, supplier_profile: supplier1, delivery_fee: 10, radius: 0) }

    let!(:suburb2) { create(:suburb, :random, state: 'VIC') }
    let!(:delivery_zone211) { create(:delivery_zone, :random, suburb: suburb2, supplier_profile: supplier1, delivery_fee: 10, radius: 0) }
    let!(:delivery_zone212) { create(:delivery_zone, :random, suburb: suburb2, supplier_profile: supplier1, delivery_fee: 10, radius: 0) }
    let!(:delivery_zone231) { create(:delivery_zone, :random, suburb: suburb2, supplier_profile: supplier3, delivery_fee: 0, radius: 0) }
    let!(:delivery_zone232) { create(:delivery_zone, :random, suburb: suburb2, supplier_profile: supplier3, delivery_fee: 10, radius: 0) }

    let!(:suburb3) { create(:suburb, :random, state: 'NSW') }
    let!(:delivery_zone341) { create(:delivery_zone, :random, suburb: suburb3, supplier_profile: supplier4, delivery_fee: 10, radius: 0) }

    let!(:suburb4) { create(:suburb, :random, state: 'NSW', longitude: 99_999, latitude: 99_999) }

    before :each do
      clear_cache
      DeliveryZone.all.each do |delivery_zone|
        DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).call
      end
    end

    it 'doesn\'t list suppliers if they don\'t deliver in that suburb' do
      lister_options = { suburb: suburb4 }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to be_empty
    end

    it 'only lists supplier that deliver in the supplied suburb' do
      lister_options = { suburb: suburb1 }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1)
      expect(suppliers).to_not include(supplier2, supplier3, supplier4)

      lister_options = { suburb: suburb2 }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier3)
      expect(suppliers).to_not include(supplier2, supplier4)
    end

    context 'with delivery suburbs within range' do
      let!(:supplier5) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier5') }
      let!(:supplier6) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier6') }

      let!(:suburb5) { create(:suburb, :random, state: 'NSW', longitude: 151.2078, latitude: -33.9031) }
      let!(:delivery_zone551) { create(:delivery_zone, :random, suburb: suburb5, supplier_profile: supplier5, radius: 10) }
      let!(:delivery_zone561) { create(:delivery_zone, :random, suburb: suburb5, supplier_profile: supplier6, radius: 0) }

      let!(:suburb6) { create(:suburb, :random, state: 'NSW', longitude: 151.2044, latitude: -33.8721) }

      before do
        DeliveryZone.all.each do |delivery_zone|
          DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).call
        end
      end

      it 'lists suppliers whose delivery range is within the supplied suburb' do
        lister_options = { suburb: suburb6 }
        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers).to include(supplier5)
        expect(suppliers).to_not include(supplier6)
      end

      it 'filters the suppliers that provide free delivery for a specific suburb' do
        clear_cache
        lister_options = { suburb: suburb2, delivery: ['free_delivery'] }
        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers).to include(supplier3)
        expect(suppliers).to_not include(supplier1, supplier2, supplier4)
      end
    end

    context 'filter supplier with delivery zone operating days and order date' do
      let!(:order_date) { Time.zone.now + 1.day }

      it 'lists supplier that operate on the passed in order date (week-day)' do
        delivery_zone341.update_column(:operating_wdays, '1111111')

        lister_options = { suburb: suburb3, order_date: order_date }
        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers).to include(supplier4)
      end

      it 'filters out supplier that do not opearate on the passed in order date (week-day)' do
        operating_wdays = 7.times.map{|num| num == order_date.wday ? '0' : '1' }.join('')
        delivery_zone341.update_column(:operating_wdays, operating_wdays)

        lister_options = { suburb: suburb3, order_date: order_date }
        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers).to_not include(supplier4)
      end
    end

    context 'with a missing suburb' do
      it 'lists all supplier (within limit)' do
        lister_options = { suburb: nil, limit: 3 }

        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers.size).to eq(3)
      end

      it 'doesn\'t return any suppliers for_react' do
        lister_options = { suburb: nil, limit: 3, for_react: true }

        suppliers = Suppliers::List.new(options: lister_options).call
        expect(suppliers).to be_blank
      end
    end
  end

  context 'category based filtering' do
    it 'only lists suppliers that provide menu section from given category group' do
      lister_options = { category_group: catering_category.group }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier4)
      expect(suppliers).to_not include(supplier3)
    end

    it 'does not list suppliers with archived menu sections for given category group', skip: 'Already tested in Suppliers::Cache::CategoryGroups' do
      menu_section14.update_column(:archived_at, Time.zone.now)
      Suppliers::Cache::CategoryGroups.new(suppliers: [menu_section14.supplier_profile]).call

      lister_options = { category_group: catering_category.group }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to_not include(supplier4)
    end

    it 'only lists suppliers that provide menu section from given categories' do
      lister_options = { category: [catering_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier4)
      expect(suppliers).to_not include(supplier2, supplier3)

      lister_options = { category: [kitchen_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2, supplier4)
      expect(suppliers).to_not include(supplier1, supplier3)

      lister_options = { category: [other_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier3)
      expect(suppliers).to_not include(supplier1, supplier2, supplier4)
    end

    it 'lists suppliers with menu section within any given multiple categories' do
      lister_options = { category: [catering_category, kitchen_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2, supplier4)
      expect(suppliers).to_not include(supplier3)
    end

    it 'does not list suppliers with an inactive menu section for given categories', skip: 'Already tested in Suppliers::Cache::CategoryGroups' do
      menu_section12.update_column(:archived_at, Time.zone.now)
      Suppliers::Cache::CategoryGroups.new(suppliers: [menu_section12.supplier_profile]).call

      lister_options = { category: [catering_category, kitchen_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to_not include(supplier2) # supplier with inactive menu section in category 2
    end

    it 'lists suppliers with menu section with all given multiple categories if cumulative' do
      menu_section11.categories << kitchen_category
      menu_section12.categories << catering_category
      menu_section13.categories << kitchen_category
      create(:menu_item, :random, name: 'menu_item13', description: 'menu_item13_description', supplier_profile: supplier3, menu_section: menu_section13)
      create(:menu_item, :random, name: 'menu_item14', description: 'menu_item14_description', supplier_profile: supplier4, menu_section: menu_section14)

      lister_options = { category: [catering_category, kitchen_category].map(&:slug), cumulative: true }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier2)
      expect(suppliers).to_not include(supplier3) # because it does not have both categories
      expect(suppliers).to_not include(supplier4) # because it does not have items within each section with given categories
    end
  end

  context 'filter by dietary preferences' do
    let!(:all_suppliers) { [supplier1, supplier2, supplier3, supplier4] }

    SupplierProfile::SUPPLIER_DIETARY_FLAGS.each do |preference|
      context "supplier with #{preference} supplier flag as true" do
        let!(:preference_suppliers) { all_suppliers.sample(2) }
        let!(:non_preference_suppliers) { all_suppliers - preference_suppliers }

        before do
          preference_suppliers.each do |supplier|
            supplier.supplier_flags.update_column(preference.to_sym, true)
          end
        end

        it "filters the suppliers by the #{preference.to_s.gsub(/has_|_items/, '').gsub('_', ' ')} dietary options" do
          lister_options = { dietary: [preference] }
          suppliers = Suppliers::List.new(options: lister_options).call

          expect(suppliers).to include(*preference_suppliers)
          expect(suppliers).to_not include(*non_preference_suppliers)
        end

        it 'filters the suppliers by a multiple dietary options (cumulative)' do
          preference2 = (SupplierProfile::SUPPLIER_DIETARY_FLAGS - [preference]).sample
          multi_preference_supplier = preference_suppliers.sample
          non_multi_preference_suppliers = preference_suppliers - [multi_preference_supplier]

          multi_preference_supplier.supplier_flags.update_column(preference2.to_sym, true)

          lister_options = { dietary: [preference, preference2] }
          suppliers = Suppliers::List.new(options: lister_options).call
          expect(suppliers).to include(multi_preference_supplier)
          expect(suppliers).to_not include(*non_preference_suppliers, *non_multi_preference_suppliers)
        end
      end # with preference flag set to true
    end # POSSIBLE_PREFERENCES
  end # filter by dietary preference

  it 'filters the suppliers by delivery options' do
    lister_options = { delivery: ['needs_swipe_card_access'] }
    suppliers = Suppliers::List.new(options: lister_options).call
    expect(suppliers).to include(supplier1)
    expect(suppliers).to_not include(supplier2, supplier3, supplier4)

    lister_options = { delivery: %w[supplies_in_working_hours provides_multi_service_point] }
    suppliers = Suppliers::List.new(options: lister_options).call
    expect(suppliers).to include(supplier3)
    expect(suppliers).to_not include(supplier1, supplier2, supplier4)
  end

  context 'with minimums' do
    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, spend_price: 40, lead_time: 240) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 50, lead_time: 10) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 100, lead_time: 20) }
    let!(:minimum23) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 110, lead_time: 32) }

    let!(:minimum31) { create(:minimum, :random, supplier_profile: supplier3, spend_price: 40, lead_time: 1) }
    let!(:minimum32) { create(:minimum, :random, supplier_profile: supplier3, spend_price: 60, lead_time: 23) }

    let!(:minimum41) { create(:minimum, :random, supplier_profile: supplier4, spend_price: 200, lead_time: 20) }

    it 'filters the suppliers by their minimum spend price' do
      lister_options = { category_group: 'catering-services', other: ['min_order'] }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1)
      expect(suppliers).to_not include(supplier2, supplier3, supplier4)

      lister_options = { other: ['min_order'] }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1, supplier3)
      expect(suppliers).to_not include(supplier2, supplier4)
    end

    it 'filters the suppliers by their maximum lead time' do
      lister_options = { other: ['lead_mode'] }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier3, supplier4)
      expect(suppliers).to_not include(supplier1, supplier2)
    end
  end

  context 'filter by search keywords' do
    it 'filters the suppliers by name' do
      lister_options = { search_keywords: supplier1.name }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1)
      expect(suppliers).to_not include(supplier2, supplier3, supplier4)
    end

    it 'filters the suppliers by supplied menu item name / description' do
      lister_options = { search_keywords: menu_item12.name }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2)
      expect(suppliers).to_not include(supplier1, supplier3, supplier4)

      lister_options = { search_keywords: menu_item11.description }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier1)
      expect(suppliers).to_not include(supplier2, supplier3, supplier4)
    end
  end

  context 'filter by closure dates' do
    let!(:order_date) { Date.today }

    it 'only lists suppliers who are not closed on the order date' do
      supplier1.update_columns(close_from: (order_date - 1.day).beginning_of_day, close_to: (order_date + 1.day).end_of_day)
      supplier3.update_columns(close_from: (order_date + 1.day).beginning_of_day, close_to: (order_date + 3.days).end_of_day)
      supplier4.update_columns(close_from: (order_date - 3.days).beginning_of_day, close_to: order_date.end_of_day)

      lister_options = { order_date: [order_date, order_date.to_s].sample }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2, supplier3)
      expect(suppliers).to_not include(supplier1, supplier4)
    end
  end

  context 'filtering for team orders', team_orders: true do
    it 'filters the suppliers who supply for team orders' do
      lister_options = { team_suppliers: true }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier2, supplier3)
      expect(suppliers).to_not include(supplier1, supplier4)
    end

    it 'filters the keyword search for menu items based on menu items being available for team orders' do
      lister_options = { team_suppliers: true, search_keywords: menu_item12.description }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to_not include(supplier2) # even if it finds the menu items within the supplier

      menu_item12.update_column(%i[team_order team_order_only].sample, true)
      lister_options = { team_suppliers: true, search_keywords: menu_item12.description }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2)
    end

    it 'filters the suppliers by name' do
      lister_options = { team_suppliers: true, search_keywords: supplier2.name }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2)
      expect(suppliers).to_not include(supplier1) # not matching name
      expect(suppliers).to_not include(supplier3, supplier4) # not team suppliers
    end

    it 'lists suppliers with menu section with all given multiple categories and containing team order items' do
      supplier1.update_column(:team_supplier, true)
      menu_section11.categories << kitchen_category
      menu_section12.categories << catering_category

      lister_options = { team_suppliers: true, category: [catering_category, kitchen_category].map(&:slug), cumulative: true }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to_not include(supplier1, supplier2) # even if it has menu sections with the category
      expect(suppliers).to_not include(supplier3, supplier4) # not team suppliers

      menu_item12.update_column(%i[team_order team_order_only].sample, true)
      lister_options = { team_suppliers: true, category: [catering_category, kitchen_category].map(&:slug) }
      suppliers = Suppliers::List.new(options: lister_options).call
      expect(suppliers).to include(supplier2)
      expect(suppliers).to_not include(supplier1) # even if it has menu sections with the category
      expect(suppliers).to_not include(supplier3, supplier4) # not team suppliers
    end
  end

  context 'meal plan suppliers', meal_plans: true do
    let!(:meal_plan_category) { create(:category, :random) }

    before do
      meal_plan_category.update_column(:slug, Category::MEAL_PLAN_CATEGORY_SLUGS.sample)
      [menu_section11, menu_section24].each do |menu_section|
        menu_section.update(categories: [meal_plan_category])
      end
    end

    it 'only lists suppliers with menu sections containing the meal plan categories' do
      lister_options = { mealUUID: SecureRandom.uuid }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier4) # it has menu sections with the meal plan category
      expect(suppliers).to_not include(supplier2, supplier3) # not meal plan suppliers
    end
  end

  context 'supplier flag based filtering' do
    it 'filters suppliers who provide contactless delivery' do
      [supplier1, supplier4].each do |supplier|
        supplier.supplier_flags.update_column(:provides_contactless_delivery, true)
      end
      lister_options = { other: ['provides_contactless_delivery'] }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier4)
      expect(suppliers).to_not include(supplier2, supplier3)
    end

    it 'filters suppliers who is set as socially responsible' do
      [supplier2, supplier3].each do |supplier|
        supplier.supplier_flags.update_column(:is_socially_responsible, true)
      end
      lister_options = { other: ['is_socially_responsible'] }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier2, supplier3)
      expect(suppliers).to_not include(supplier1, supplier4)
    end

    it 'filters suppliers who is set as eco-friendly' do
      [supplier1, supplier3].each do |supplier|
        supplier.supplier_flags.update_column(:is_eco_friendly, true)
      end
      lister_options = { other: ['is_eco_friendly'] }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier3)
      expect(suppliers).to_not include(supplier2, supplier4)
    end

    it 'filters suppliers who is set as indigenous-owned' do
      [supplier2, supplier4].each do |supplier|
        supplier.supplier_flags.update_column(:is_indigenous_owned, true)
      end
      lister_options = { other: ['is_indigenous_owned'] }
      suppliers = Suppliers::List.new(options: lister_options).call

      expect(suppliers).to include(supplier2, supplier4)
      expect(suppliers).to_not include(supplier1, supplier3)
    end
  end

  it 'can filter suppliers on multiple options' do
    clear_cache
    lister_options = { is_searchable: true, search_keywords: menu_item11.name }
    suppliers = Suppliers::List.new(options: lister_options).call
    expect(suppliers).to include(supplier1)
    expect(suppliers).to_not include(supplier2, supplier3, supplier4)
  end

  def clear_cache
    Rails.cache.clear
  end

end
