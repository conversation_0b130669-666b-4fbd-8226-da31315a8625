require 'rails_helper'

RSpec.describe Orders::Cancel, type: :service, orders: true do

  let!(:supplier1) { create(:supplier_profile, :random, company_name: 'supplier1') }
  let!(:supplier2) { create(:supplier_profile, :random, company_name: 'supplier2') }

  before do
    # mock that the email will be sent
    email_sender = delayed_email_sender = double(Suppliers::Emails::SendOrderCancelledEmail)
    allow(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  context 'a one-off order' do
    let!(:order) { create(:order, :confirmed, order_type: 'one-off') }
    let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

    before do
      refunder = delayed_refunder = double(Orders::OnHoldCharges::Refund)
      allow(Orders::OnHoldCharges::Refund).to receive(:new).and_return(refunder)
      allow(refunder).to receive(:delay).and_return(delayed_refunder)
      refund_response = OpenStruct.new(success?: true, errors: [], refunds: ['refund'])
      allow(delayed_refunder).to receive(:call).and_return(refund_response)
    end

    it 'sets the order as cancelled' do
      order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call

      expect(order_canceller).to be_success
      expect(order.reload.status).to eq('cancelled')
      expect(order_canceller.cancelled_orders).to include(order)
    end

    it 'sends an order cancelled email to the order suppliers with mode = single', notifications: true do
      expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: supplier1, orders: [order])

      order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
      expect(order_canceller).to be_success
    end

    it 'logs an `Order Cancelled` event', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'order-cancelled', severity: 'warning')

      order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
      expect(order_canceller).to be_success
    end

    context 'with order charges' do
      it 'does not request to refund on-hold charges if order is connected to (stripe) credit card' do
        expect(Orders::OnHoldCharges::Refund).to_not receive(:new).with(order: order)

        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
        expect(order_canceller).to be_success
      end

      it 'requests to refund on-hold charges if order is connected to (stripe) credit card' do
        stripe_card = create(:credit_card, :valid_stripe_payment)
        order.update_column(:credit_card_id, stripe_card.id)
        order.reload

        expect(Orders::OnHoldCharges::Refund).to receive(:new).with(order: order)
        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call

        expect(order_canceller).to be_success
      end
    end

    context 'void an order' do
      let!(:cancel_mode) { 'void' }

      it 'sets the order as voided' do
        order_canceller = Orders::Cancel.new(order: order, mode: cancel_mode).call

        expect(order_canceller).to be_success
        expect(order.reload.status).to eq('voided')
        expect(order_canceller.cancelled_orders).to include(order)
      end

      it 'does not request to refund on-hold charges if order is connected to (stripe) credit card' do
        expect(Orders::OnHoldCharges::Refund).to_not receive(:new).with(order: order)

        order_canceller = Orders::Cancel.new(order: order, mode: cancel_mode).call
        expect(order_canceller).to be_success
      end

      it 'requests to refund on-hold charges if order is connected to (stripe) credit card' do
        stripe_card = create(:credit_card, :valid_stripe_payment)
        order.update_column(:credit_card_id, stripe_card.id)
        order.reload

        expect(Orders::OnHoldCharges::Refund).to receive(:new).with(order: order)
        order_canceller = Orders::Cancel.new(order: order, mode: cancel_mode).call

        expect(order_canceller).to be_success
      end

      it 'does not send an order order cancelled email to the order suppliers', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to_not receive(:new)

        order_canceller = Orders::Cancel.new(order: order, mode: cancel_mode).call
        expect(order_canceller).to be_success
      end

      it 'allows a delivered order to be voided', notifications: true do
        order.update_column(:status, 'delivered')
        expect(Suppliers::Emails::SendOrderCancelledEmail).to_not receive(:new) # does not send email

        order_canceller = Orders::Cancel.new(order: order, mode: cancel_mode).call
        expect(order_canceller).to be_success
        expect(order.reload.status).to eq('voided')
        expect(order_canceller.cancelled_orders).to include(order)
      end
    end

    context 'as a team order' do
      let!(:team_supplier) { create(:supplier_profile, :random) }
      let!(:order_supplier) { create(:order_supplier, order: order, supplier_profile: team_supplier) }

      before do
        order.update_columns(status: 'pending', order_variant: %w[team_order recurring_team_order].sample)
      end

      it 'sends an order cancelled email to team supplier profiles if a pending team order is cancelled', team_orders: true, notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to_not receive(:new).with(mode: 'one-off', supplier: supplier1, orders: [order]) # not order line suppliers

        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: team_supplier, orders: [order]) # team suppliers
        order_canceller = Orders::Cancel.new(order: order.reload, mode: 'one-off').call
        expect(order_canceller).to be_success
        expect(order.reload.status).to eq('cancelled')
      end
    end

    context 'quoted order' do
      before do
        order.update_columns(status: 'quoted')
      end

      it 'does not send an email to the order suppliers', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to_not receive(:new).with(mode: 'one-off', supplier: supplier1, orders: [order])

        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
        expect(order_canceller).to be_success
      end

      it 'marks the the suppliers as notified with the order creation date' do
        order.update_column(:created_at, Time.zone.now - 2.days)
        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
        expect(order_canceller).to be_success

        cancelled_order = order.reload
        expect(cancelled_order.status).to eq('cancelled')
        expect(cancelled_order.suppliers_notified_at).to be_present
        expect(cancelled_order.suppliers_notified_at.to_s).to eq(order.created_at.to_s)
      end
    end # quoted order

    context 'a Woolworths Order' do
      let!(:orders_email) { 'orders-email' }
      let!(:woolworths_order) { create(:woolworths_order, :random, order: order)}

      before do
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return(orders_email)
      end

      it 'only lets admins cancel a Woolworths Order' do
        # as Non-Admin
        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off', is_admin: false).call

        expect(order_canceller).to_not be_success
        expect(order_canceller.errors).to include("Only Yordar Admins can cancel a Woolworths Order. Please get in touch with Yordar Admin via #{orders_email}")

        # as Admin
        order_canceller = Orders::Cancel.new(order: order, mode: 'one-off', is_admin: true).call
        expect(order_canceller).to be_success

        cancelled_order = order.reload
        expect(cancelled_order.status).to eq('cancelled')
      end
    end # Woolworths Order
  end # one-off change

  context 'recurring orders' do
    # root order
    let!(:order1) { create(:order, :confirmed, name: 'order1', order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: %w[mon wed] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-09 12:00:00')) }
    let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
    let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

    # Monday orders
    let!(:order2) { create(:order, :confirmed, name: 'order2', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-16 12:00:00')) }
    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
    let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

    let!(:order3) { create(:order, :confirmed, name: 'order3', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-23 12:00:00')) }
    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }
    let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

    let!(:order4) { create(:order, :confirmed, name: 'order4', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-30 12:00:00')) }
    let!(:order_line41) { create(:order_line, :random, order: order4, supplier_profile: supplier1) }
    let!(:order_line42) { create(:order_line, :random, order: order4, supplier_profile: supplier2) }

    let!(:order5) { create(:order, :confirmed, name: 'order5', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-04-06 12:00:00')) }
    let!(:order_line51) { create(:order_line, :random, order: order5, supplier_profile: supplier1) }
    let!(:order_line52) { create(:order_line, :random, order: order5, supplier_profile: supplier2) }

    # Wednesday orders
    let!(:order6) { create(:order, :confirmed, name: 'order6', recurrent_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-11 12:00:00')) }
    let!(:order_line61) { create(:order_line, :random, order: order6, supplier_profile: supplier1) }
    let!(:order_line62) { create(:order_line, :random, order: order6, supplier_profile: supplier2) }

    let!(:order7) { create(:order, :confirmed, name: 'order7', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-18 12:00:00')) }
    let!(:order_line71) { create(:order_line, :random, order: order7, supplier_profile: supplier1) }
    let!(:order_line72) { create(:order_line, :random, order: order7, supplier_profile: supplier2) }

    let!(:order8) { create(:order, :confirmed, name: 'order8', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-25 12:00:00')) }
    let!(:order_line81) { create(:order_line, :random, order: order8, supplier_profile: supplier1) }
    let!(:order_line82) { create(:order_line, :random, order: order8, supplier_profile: supplier2) }

    let!(:order9) { create(:order, :confirmed, name: 'order9', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-04-01 12:00:00')) }
    let!(:order_line91) { create(:order_line, :random, order: order9, supplier_profile: supplier1) }
    let!(:order_line92) { create(:order_line, :random, order: order9, supplier_profile: supplier2) }

    before do
      order1.update_columns(recurrent_id: order1.id, template_id: order1.id)
      order6.update_columns(template_id: order6.id)
    end

    context 'skip a single order' do

      it 'sets it as cancelled' do
        order_canceller = Orders::Cancel.new(order: order2, mode: 'one-off').call
        expect(order_canceller).to be_success
        order2.reload

        expect(order2.status).to eq('cancelled')
        expect(order2.order_type).to eq('one-off')

        # does not cancel any other orders
        [order1, order3, order4, order5].each(&:reload).each do |order|
          expect(order.status).to_not eq('cancelled')
          expect(order.template_id).to eq(order1.id)
        end
      end

      it 'sends an order cancelled email to the order suppliers with mode = single', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: supplier1, orders: [order2])
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: supplier2, orders: [order2])

        order_canceller = Orders::Cancel.new(order: order2, mode: 'one-off').call
        expect(order_canceller).to be_success
      end

      it 'logs an `Order Cancelled` event', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: order2, event: 'order-cancelled', severity: 'warning')

        order_canceller = Orders::Cancel.new(order: order2, mode: 'one-off').call
        expect(order_canceller).to be_success
      end
    end

    context 'Permanently cancel all subsequent orders' do

      it 'only cancels all subsequent orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'subsequent').call

        expect(order_canceller).to be_success
        [order3, order4, order5].each(&:reload).each do |order|
          expect(order.status).to eq('cancelled')
          expect(order.template_id).to eq(order3.id)
        end
      end

      it 'does not cancel any past orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'subsequent').call

        expect(order_canceller).to be_success
        [order1, order2].each(&:reload).each do |order|
          expect(order.status).to_not eq('cancelled')
          expect(order.template_id).to eq(order1.id)
        end
      end

      it 'does not cancels any subsequent related orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'subsequent').call

        expect(order_canceller).to be_success
        [order6, order7, order8, order9].each(&:reload).each do |order|
          expect(order.status).to_not eq('cancelled')
          expect(order.template_id).to eq(order6.id)
        end
      end

      it 'sends an order cancelled email to the order suppliers with mode = subsequent', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'subsequent', supplier: supplier1, orders: [order3, order4, order5].sort_by(&:id))
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'subsequent', supplier: supplier2, orders: [order3, order4, order5].sort_by(&:id))

        order_canceller = Orders::Cancel.new(order: order3, mode: 'subsequent').call
        expect(order_canceller).to be_success
      end

      it 'logs an `Order Cancelled Permanently` event', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: order3, event: 'order-cancelled-permanently', severity: 'warning')

        order_canceller = Orders::Cancel.new(order: order3, mode: 'subsequent').call
        expect(order_canceller).to be_success
      end
    end

    context 'Permanently cancel all subsequent and related orders' do

      it 'only cancels subsequent related orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'related').call

        expect(order_canceller).to be_success
        [order3, order4, order5].each(&:reload).each do |order|
          expect(order.status).to eq('cancelled')
          expect(order.template_id).to eq(order1.id)
        end

        [order8, order9].each(&:reload).each do |order|
          expect(order.status).to eq('cancelled')
          expect(order.template_id).to eq(order6.id)
        end
      end

      it 'does not cancel any previous orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'related').call
        [order1, order2, order3, order4, order5, order6, order6, order8, order9].each(&:reload)

        expect(order_canceller).to be_success
        # does not cancel previous orders
        [order1, order2].each(&:reload).each do |order|
          expect(order.status).to_not eq('cancelled')
          expect(order.template_id).to eq(order1.id)
        end

        # does not cancel related previous orders
        [order6, order7].each(&:reload).each do |order|
          expect(order.status).to_not eq('cancelled')
          expect(order.template_id).to eq(order6.id)
        end
      end

      it 'sends an order cancelled email to the order suppliers with mode = related', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'related', supplier: supplier1, orders: [order3, order4, order5, order8, order9].sort_by(&:id))
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'related', supplier: supplier2, orders: [order3, order4, order5, order8, order9].sort_by(&:id))

        order_canceller = Orders::Cancel.new(order: order3, mode: 'related').call
        expect(order_canceller).to be_success
      end

      it 'logs an `Order Cancelled Permanently` event', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: order3, event: 'order-cancelled-permanently', severity: 'warning')

        order_canceller = Orders::Cancel.new(order: order3, mode: 'related').call
        expect(order_canceller).to be_success
      end
    end

    context 'Putting a recurring order on hold' do

      it 'only pauses all subsequent orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'on-hold').call

        expect(order_canceller).to be_success
        [order3, order4, order5].each(&:reload).each do |order|
          expect(order.status).to eq('paused')
          expect(order.template_id).to eq(order3.id)
        end
      end

      it 'does not pause any past orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'on-hold').call

        expect(order_canceller).to be_success
        [order1, order2].each(&:reload).each do |order|
          expect(order.status).to_not eq('paused')
          expect(order.template_id).to eq(order1.id)
        end
      end

      it 'does not pause any subsequent related orders' do
        order_canceller = Orders::Cancel.new(order: order3, mode: 'on-hold').call

        expect(order_canceller).to be_success
        [order6, order7, order8, order9].each(&:reload).each do |order|
          expect(order.status).to_not eq('paused')
          expect(order.template_id).to eq(order6.id)
        end
      end

      it 'sends an order paused email to the order suppliers', notifications: true do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'on-hold', supplier: supplier1, orders: [order3, order4, order5].sort_by(&:id))
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'on-hold', supplier: supplier2, orders: [order3, order4, order5].sort_by(&:id))

        order_canceller = Orders::Cancel.new(order: order3, mode: 'on-hold').call
        expect(order_canceller).to be_success
      end

      it 'does not log any event', event_logs: true do
        expect(EventLogs::Create).to_not receive(:new)

        order_canceller = Orders::Cancel.new(order: order3, mode: 'on-hold').call
        expect(order_canceller).to be_success
      end
    end
  end
end
