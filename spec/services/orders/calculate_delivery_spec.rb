require 'rails_helper'

RSpec.describe Orders::CalculateDelivery, type: :service, orders: true do

  context 'with no order lines' do
    let(:order) { create(:order, :draft) }

    it 'return 0 if there are no order lines' do
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(0.0)
    end
  end

  let!(:customer) { create(:customer_profile, :random) }

  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }

  let(:suburb) { create(:suburb, :random) }
  let(:order) { create(:order, :draft, customer_profile: customer, no_delivery_charge: false) }

  let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier1, price: 1.0, quantity: 5, category: nil) }
  let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier1, price: 1.0, quantity: 5, category: nil) }
  let!(:order_line13) { create(:order_line, :random, order: order, supplier_profile: supplier1, price: 1.0, quantity: 5, category: nil) }

  let!(:order_line21) { create(:order_line, :random, order: order, supplier_profile: supplier2, price: 1.0, quantity: 5, category: nil) }
  let!(:order_line22) { create(:order_line, :random, order: order, supplier_profile: supplier2, price: 1.0, quantity: 5, category: nil) }
  let!(:order_line23) { create(:order_line, :random, order: order, supplier_profile: supplier2, price: 1.0, quantity: 5, category: nil) }

  let!(:override_fetcher) { double(DeliveryOverrides::FetchOverride) }

  before do
    # default to no overrides
    allow(DeliveryOverrides::FetchOverride).to receive(:new).and_return(override_fetcher)
    allow(override_fetcher).to receive(:call).and_return(nil)
  end

  before do
    order.reload # to attach order lines to order after creation
  end

  it 'returns if no supplier fee or mimimum spends are setup' do
    calculated_delivery = Orders::CalculateDelivery.new(order: order).call

    expect(calculated_delivery).to eq(0.0)
  end

  context 'supplier with delivery zones with fee', deliverable_suburbs: true do
    let(:order) { create(:order, :draft, delivery_suburb: suburb) }
    let!(:delivery_zone11) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, radius: 1, delivery_fee: 10) }
    let!(:delivery_zone12) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, radius: 1, delivery_fee: 20) }

    let!(:delivery_zone12) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier2, radius: 1, delivery_fee: 27) }
    let!(:delivery_zone22) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier2, radius: 1, delivery_fee: 13) }

    before do
      supplier1_delivery_zone_fetcher = double(Suppliers::FetchPotentialDeliveryZone)
      allow(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier1, suburb: order.delivery_suburb, delivery_date: order.delivery_at).and_return(supplier1_delivery_zone_fetcher)
      allow(supplier1_delivery_zone_fetcher).to receive(:call).and_return(delivery_zone11)

      supplier1_delivery_zone_fetcher = double(Suppliers::FetchPotentialDeliveryZone)
      allow(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: order.delivery_suburb, delivery_date: order.delivery_at).and_return(supplier1_delivery_zone_fetcher)
      allow(supplier1_delivery_zone_fetcher).to receive(:call).and_return(delivery_zone22)
    end

    let!(:delivery_zone_based_fee) { delivery_zone11.delivery_fee + delivery_zone22.delivery_fee }

    it 'makes a request to fetch potential delivery zone for each supplier' do
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier1, suburb: order.delivery_suburb, delivery_date: order.delivery_at)
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: order.delivery_suburb, delivery_date: order.delivery_at)

      Orders::CalculateDelivery.new(order: order).call
    end

    it 'returns the cumulative (minimum) delivery fee for the suppliers' do
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(delivery_zone_based_fee)
    end

    it 'if adds (minimum) delivery fee per supplier for an event_order with no custom fees' do
      order.update_column(:order_variant, 'event_order')
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(delivery_zone_based_fee)
    end

    it 'returns 0 if no_delivery_charge is set on the order' do
      order.update_column(:no_delivery_charge, true)
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(0.0)
    end

    context 'with passed in profile' do
      it 'returns calculated all order suppliers delivery zone based fee for order\'s customer' do
        calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

        expect(calculated_delivery).to eq(delivery_zone_based_fee)
      end

      it 'returns supplier specific delivery zone based fee for passed in supplier' do
        calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
        expect(calculated_delivery1).to eq(delivery_zone11.delivery_fee)

        calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
        expect(calculated_delivery2).to eq(delivery_zone22.delivery_fee)
      end
    end

    context 'if no_delivery_charge is set on the order' do
      before do
        order.update_column(:no_delivery_charge, true)
      end

      it 'returns delivery fee of 0' do
        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(0.0)
      end

      it 'returns delivery zone based fee if asked to calculate default', calculate_defaut: true do
        calculated_delivery = Orders::CalculateDelivery.new(order: order, calculate_default: true).call

        expect(calculated_delivery).to eq(delivery_zone_based_fee)
      end
    end

    context 'with order (supplier) delivery overrides' do
      let!(:order_supplier1) { create(:order_supplier, :random, order: order, supplier_profile: supplier1, delivery_fee_override: rand(2.3..20.9)) }
      let!(:order_supplier2) { create(:order_supplier, :random, order: order, supplier_profile: supplier2, delivery_fee_override: rand(2.3..20.9)) }

      context 'for a customer' do
        it 'returns the cumulative overridden fee' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(order_supplier1.delivery_fee_override + order_supplier2.delivery_fee_override)
        end

        it 'returns the cumulative delivery zone based delivery fee if order_supplier does not contain any delivery_fee_overrides' do
          [order_supplier1, order_supplier2].each{|order_supplier| order_supplier.update_column(:delivery_fee_override, nil) }
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(delivery_zone_based_fee)
        end

        it 'returns the cumulative delivery zone based delivery fee if asked to calculate default', calculate_defaut: true do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer, calculate_default: true).call

          expect(calculated_delivery).to eq(delivery_zone_based_fee)
        end
      end

      context 'for a supplier' do
        it 'returns the overridden suppler fee for the passed in supplier' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
          expect(calculated_delivery).to eq(order_supplier1.delivery_fee_override)

          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
          expect(calculated_delivery).to eq(order_supplier2.delivery_fee_override)
        end

        it 'returns supplier specific delivery zone based fee if order_supplier does not contain any delivery_fee_overrides' do
          [order_supplier1, order_supplier2].each{|order_supplier| order_supplier.update_column(:delivery_fee_override, nil) }

          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
          expect(calculated_delivery1).to eq(delivery_zone11.delivery_fee)

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
          expect(calculated_delivery2).to eq(delivery_zone22.delivery_fee)
        end
      end # for a supplier
    end

    context 'with delivery overrides' do
      let!(:delivery_override) { create(:delivery_override, :random, customer_profile: customer, customer_override: rand(2.3..20.9), supplier_override: rand(2.3..20.9)) }

      before do
        allow(override_fetcher).to receive(:call).and_return(delivery_override)
      end

      context 'for a customer' do
        it 'returns the cumulative overridden customer fee' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(delivery_override.customer_override + delivery_override.customer_override) # twice cause of 2 suppliers
        end

        it 'returns the cumulative delivery zone based fee if delivery overrides do not contain customer overrides' do
          delivery_override.update_column(:customer_override, nil)
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(delivery_zone_based_fee)
        end

        it 'returns the cumulative delivery zone based fee if asked to calculate default', calculate_defaut: true do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer, calculate_default: true).call

          expect(calculated_delivery).to eq(delivery_zone_based_fee)
        end
      end

      context 'for a supplier' do
        it 'returns the cumulative overridden suppler fee' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: [supplier1, supplier2].sample).call

          expect(calculated_delivery).to eq(delivery_override.supplier_override) # same delivery override fetched irrespective of supplier
        end

        it 'returns supplier specific delivery zone based fee if delivery overrides do not contain any supplier overrides' do
          delivery_override.update_column(:supplier_override, nil)
          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
          expect(calculated_delivery1).to eq(delivery_zone11.delivery_fee)

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
          expect(calculated_delivery2).to eq(delivery_zone22.delivery_fee)
        end

        it 'returns supplier specific delivery zone based fee if asked to calculate default', calculate_defaut: true do
          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1, calculate_default: true).call
          expect(calculated_delivery1).to eq(delivery_zone11.delivery_fee)

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2, calculate_default: true).call
          expect(calculated_delivery2).to eq(delivery_zone22.delivery_fee)
        end
      end # for a supplier
    end # with delivery overrides
  end

  context 'supplier with mimumum spends and minimum delivery fee' do
    let(:supplier1) { create(:supplier_profile, :random, minimum_delivery_fee: 22, company_name: 'supplier1') }
    let(:supplier2) { create(:supplier_profile, :random, minimum_delivery_fee: 17, company_name: 'supplier2') }

    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, spend_price: 100) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, spend_price: 10) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 30) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 100) }

    let!(:minimum_based_fee) { supplier1.minimum_delivery_fee + supplier2.minimum_delivery_fee }

    it 'if order spend for supplier is less than mimimum spend for supplier it adds the mimimum spend fee of that supplier' do
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(minimum_based_fee)
    end

    it 'if adds the mimimum spend fee of that supplier for an event_order with no custom fees' do
      order.update_column(:order_variant, 'event_order')
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(minimum_based_fee)
    end

    context 'for a customer' do
      it 'returns calculated all order suppliers minimum based fee for order\'s customer' do
        calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

        expect(calculated_delivery).to eq(minimum_based_fee)
      end

      context 'with saved order.customer_delivery' do
        let!(:saved_delivery_fee) { rand(20.2..30.3) }
        before do
          order.update_column(:customer_delivery, saved_delivery_fee)
        end

        it 'returns the saved delivery fee' do
         calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

         expect(calculated_delivery.round(2).to_s).to eq(saved_delivery_fee.round(2).to_s)
        end

        it 'returns the calculated delivery fee if set to recalculate' do
         calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer, recalculate: true).call

         expect(calculated_delivery.round(2).to_s).to eq(minimum_based_fee.round(2).to_s)
        end
      end
    end

    context 'for a supplier' do
      it 'returns supplier specific minimum based fee for passed in supplier' do
        calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
        expect(calculated_delivery1).to eq(supplier1.minimum_delivery_fee)

        calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
        expect(calculated_delivery2).to eq(supplier2.minimum_delivery_fee)
      end

      context 'with saved order.customer_delivery' do
        let!(:saved_delivery_fee) { rand(20.2..30.3) }
        before do
          order.update_column(:customer_delivery, saved_delivery_fee)
        end

        it 'returns the calculated irrespective of recalculate' do
          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1, recalculate: [true, false].sample).call
          expect(calculated_delivery1).to eq(supplier1.minimum_delivery_fee)

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2, recalculate: [true, false].sample).call
          expect(calculated_delivery2).to eq(supplier2.minimum_delivery_fee)
        end
      end
    end

    context 'with delivery overrides' do
      let!(:delivery_override) { create(:delivery_override, :random, customer_profile: customer, customer_override: rand(2.3..20.9), supplier_override: rand(2.3..20.9)) }

      before do
        allow(override_fetcher).to receive(:call).and_return(delivery_override)
      end

      context 'for a customer' do
        it 'returns the cumulative overridden customer fee' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(delivery_override.customer_override + delivery_override.customer_override) # twice cause of 2 suppliers
        end

        it 'returns calculated all order suppliers minimum based fee if delivery overrides do not contain customer overrides' do
          delivery_override.update_column(:customer_override, nil)
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(minimum_based_fee)
        end
      end

      context 'for a supplier' do
        it 'returns the cumulative overridden supplier fee' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: [supplier1, supplier2].sample).call

          expect(calculated_delivery).to eq(delivery_override.supplier_override) # same delivery override fetched irrespective of supplier
        end

        it 'returns supplier specific minimum based fee if delivery overrides do not contain any supplier overrides' do
          delivery_override.update_column(:supplier_override, nil)
          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
          expect(calculated_delivery1).to eq(supplier1.minimum_delivery_fee)

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
          expect(calculated_delivery2).to eq(supplier2.minimum_delivery_fee)
        end
      end # for a supplier
    end # with delivery overrides

    it 'returns 0 if the minimum delivery fee is not set' do
      [supplier1, supplier2].each do |supplier|
        supplier.update_column(:minimum_delivery_fee, nil)
      end
      calculated_delivery = Orders::CalculateDelivery.new(order: order).call

      expect(calculated_delivery).to eq(0)
    end

    context 'with order line category based minimums' do
      let!(:catering_category1) { create(:category, :random, group: 'catering-services') }
      let!(:catering_category2) { create(:category, :random, group: 'catering-services') }

      let!(:pantry_category1) { create(:category, :random, group: 'kitchen-supplies') }
      let!(:pantry_category2) { create(:category, :random, group: 'kitchen-supplies') }

      before do
         # supplier1 with catering category
        [order_line11, order_line12].each do |order_line|
          order_line.update_column(:category_id, catering_category1.id)
        end
        minimum12.update_column(:category_id, catering_category1.id)
        minimum11.update_column(:category_id, pantry_category1.id)

        # supplier2 with pantry category
        [order_line21, order_line22].each do |order_line|
          order_line.update_column(:category_id, pantry_category1.id)
        end
        minimum21.update_column(:category_id, pantry_category1.id)
        minimum22.update_column(:category_id, catering_category1.id)
      end

      let!(:category_based_fee) { supplier2.minimum_delivery_fee }

      it 'only adds minimum delivery fee if order line category spend is less than minimum' do
        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(category_based_fee) # supplier1 minimum of $10 met hence $0 and supplier2 minimum of $30 not net hence $17
      end

      it 'only addes minimum delivery fee if order line category (group based) spend is less than minimum' do
        minimum12.update_column(:category_id, catering_category2.id) # minimum is same category group as order line categories

        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(category_based_fee) # supplier1 minimum of $10 met (due to category group minimum) hence $0 and supplier2 minimum of $30 not net hence $17
      end

      it 'addes minimum delivery fee if order lines do not have a category and total spend is less than highest non-category minimums' do
        [order_line11, order_line12].each do |order_line|
          order_line.update_column(:category_id, nil) # supplier1 order lines do not have a category
        end
        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(minimum_based_fee) # supplier1 minimum of $100 not met hence $22 and supplier2 minimum of $30 not net hence $17
      end

      it 'addes minimum delivery fee if supplier does not have any order line category (or group) based minimums and total spend is less than highest non-category minimums' do
        minimum12.update_column(:category_id, pantry_category1.id)

        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(minimum_based_fee) # supplier1 minimum of $100 not met hence $22 and supplier2 minimum of $30 not net hence $17
      end

      it 'calculates minimum spend of order based on all order line categories' do
        order_line13.update_column(:category_id, pantry_category1.id)

        calculated_delivery = Orders::CalculateDelivery.new(order: order).call

        expect(calculated_delivery).to eq(minimum_based_fee) # supplier1 minimum of $100 not met (dues to having order_line13 having pantry_category1) hence $22 and supplier2 minimum of $30 not net hence $17
      end

      context 'with passed in profile' do
        it 'returns calculated all order suppliers minimum based fee for order\'s customer' do
          calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

          expect(calculated_delivery).to eq(category_based_fee)
        end

        it 'returns supplier specific minimum based fee for passed in supplier' do
          calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
          expect(calculated_delivery1).to eq(0.0) # supplier spend has reached minimum spend

          calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
          expect(calculated_delivery2).to eq(supplier2.minimum_delivery_fee)
        end
      end

      context 'with delivery overrides' do
        let!(:delivery_override) { create(:delivery_override, :random, customer_profile: customer, customer_override: rand(2.3..20.9), supplier_override: rand(2.3..20.9)) }

        before do
          allow(override_fetcher).to receive(:call).and_return(delivery_override)
        end

        context 'for a customer' do
          it 'returns the cumulative overridden customer fee' do
            calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

            expect(calculated_delivery).to eq(delivery_override.customer_override + delivery_override.customer_override) # twice cause of 2 suppliers
          end

          it 'returns calculated all order suppliers category based fee if delivery overrides do not contain customer overrides' do
            delivery_override.update_column(:customer_override, nil)
            calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: customer).call

            expect(calculated_delivery).to eq(category_based_fee)
          end
        end

        context 'for a supplier' do
          it 'returns the cumulative overridden supplier fee' do
            calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: [supplier1, supplier2].sample).call

            expect(calculated_delivery).to eq(delivery_override.supplier_override) # same delivery override fetched irrespective of supplier
          end

          it 'returns supplier specific minimum based fee if delivery overrides do not contain any supplier overrides' do
            delivery_override.update_column(:supplier_override, nil)
            calculated_delivery1 = Orders::CalculateDelivery.new(order: order, profile: supplier1).call
            expect(calculated_delivery1).to eq(0.0) # supplier spend has reached minimum spend

            calculated_delivery2 = Orders::CalculateDelivery.new(order: order, profile: supplier2).call
            expect(calculated_delivery2).to eq(supplier2.minimum_delivery_fee)
          end
        end # for a supplier
      end # with delivery overrides

    end # with order line category minimums
  end # with supplier minimums

  context 'for a woolworths order', woolworths: true do
    let!(:worder) { create(:order, :random, delivery_suburb: suburb) }
    let!(:woolworths_order) { create(:woolworths_order, :random, order: worder, delivery_fee: 19.0) }

    let!(:order_line11) { create(:order_line, :random, order: worder, supplier_profile: supplier1, price: 1.0, quantity: 5) }
    let!(:order_line12) { create(:order_line, :random, order: worder, supplier_profile: supplier1, price: 1.0, quantity: 5) }
    let!(:order_line13) { create(:order_line, :random, order: worder, supplier_profile: supplier1, price: 1.0, quantity: 5) }

    let!(:delivery_zone11) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, radius: 1, delivery_fee: 10) }
    let!(:delivery_zone12) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, radius: 1, delivery_fee: 20) }

    let!(:woolworths_order_based_fee) { woolworths_order&.delivery_fee }

    before do
      # make supplier1 Woolworths
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier1.id)
    end

    it 'returns the saved delivery fee' do
      calculated_delivery = Orders::CalculateDelivery.new(order: worder).call

      expect(calculated_delivery).to eq(woolworths_order_based_fee)
    end

    it 'returns the minimum delivery fee for the suppliers if delivery fee is not saved for the woolworths order' do
      woolworths_order.update_column(:delivery_fee, nil)
      calculated_delivery = Orders::CalculateDelivery.new(order: worder).call

      expect(calculated_delivery).to eq(10)
    end
  end

  context 'with delivery overrides' do
    let!(:delivery_override) { create(:delivery_override, :random, customer_profile: customer, customer_override: rand(2.3..20.9), supplier_override: rand(2.3..20.9)) }

    before do
      allow(override_fetcher).to receive(:call).and_return(delivery_override)
    end

    it 'returns base delivery if no profile is passed' do
      calculated_delivery = Orders::CalculateDelivery.new(order: order, profile: nil).call

      expect(calculated_delivery).to eq(0.0) # no supplier fee or mimimum spends are setup
    end
  end

end
