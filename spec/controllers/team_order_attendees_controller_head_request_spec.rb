require 'rails_helper'

RSpec.describe Team<PERSON>rder<PERSON><PERSON>deesController, type: :controller do
  let!(:team_order) { create(:order, :draft, order_variant: 'team_order') }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, status: 'pending') }

  describe 'HEAD requests to unsubscribe endpoints' do
    context '#unsubscribe' do
      it 'does not execute unsubscribe logic for HEAD requests' do
        expect(TeamOrderAttendees::Unsubscribe).not_to receive(:new)
        
        head :unsubscribe, params: { code: team_order_attendee.uniq_code }
        
        expect(response).to have_http_status(:success)
        expect(team_order_attendee.reload.status).to eq('pending') # Status should not change
      end

      it 'executes unsubscribe logic for POST requests' do
        expect(TeamOrderAttendees::Unsubscribe).to receive(:new).and_call_original
        
        post :unsubscribe, params: { code: team_order_attendee.uniq_code }
        
        expect(response).to have_http_status(:success)
        expect(team_order_attendee.reload.status).to eq('declined') # Status should change
      end
    end

    context '#unsubscribe_package' do
      let!(:package_team_order) { create(:order, :draft, order_variant: 'team_order') }
      let!(:package_attendee) { create(:team_order_attendee, :random, order: package_team_order, status: 'pending') }

      before do
        # Mock package order setup
        allow_any_instance_of(TeamOrderAttendees::UnsubscribeWithinPackage).to receive(:call).and_return(
          double(success?: true, errors: [], unsubscribed_attendees: [package_attendee])
        )
      end

      it 'does not execute unsubscribe logic for HEAD requests' do
        expect(TeamOrderAttendees::UnsubscribeWithinPackage).not_to receive(:new)
        
        head :unsubscribe_package, params: { code: package_attendee.uniq_code }
        
        expect(response).to have_http_status(:success)
        expect(package_attendee.reload.status).to eq('pending') # Status should not change
      end

      it 'executes unsubscribe logic for POST requests' do
        expect(TeamOrderAttendees::UnsubscribeWithinPackage).to receive(:new).and_call_original
        
        post :unsubscribe_package, params: { code: package_attendee.uniq_code }
        
        expect(response).to have_http_status(:success)
      end
    end
  end
end
