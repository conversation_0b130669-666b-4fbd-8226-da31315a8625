class TeamOrderAttendeesController < ApplicationController

  before_action :fetch_team_order, only: %i[new create]
  before_action :redirect_to_next_app_team_order_attendee_order, only: :order, if: :can_redirect_to_next_app?

  # using (single) team order invitation magic link
  def new
    if @team_order.present?
      # cannot attach anonymous attendees to team orders 1 hour before cutoff
      lead_time_fetcher = Orders::FetchLeadTime.new(order: @team_order).call
      @team_order = nil if (lead_time_fetcher.lead_time - TeamOrderAttendee::REGISTRATION_CUTOFF_THRESHOLD) <= Time.zone.now
    end
    render layout: 'team_order'
  end

  # using team order package invitation magic link
  def new_for_package
    @team_order = Order.joins(:team_order_detail).where(team_order_details: { package_id: params[:package_id] }).order(:delivery_at).first
    render 'new', layout: 'team_order'
  end

  # quick link to create team order attendee for a specific team order within a package
  def new_within_package
    team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: session_profile).call
    if team_order_attendee.present?
      redirect_to next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code)
    end
  end

  def create
    attendee_attachers = TeamOrderAttendees::AddAsAnonymous.new(team_order: @team_order, attendee_params: event_attendee_params).call
    if attendee_attachers.success?
      @team_order_attendee = attendee_attachers.team_order_attendee
      render 'team_order_attendees/success', layout: 'team_order'
    else
      flash[:error] = attendee_attachers.errors.compact.join('.') if attendee_attachers.errors.present?
      flash[:warning] = attendee_attachers.warnings.compact.join('.') if attendee_attachers.warnings.present?
      render :new, layout: 'team_order'
    end
  end

  def unsubscribe
    # For HEAD requests (from prefetching), don't execute unsubscribe logic
    if request.head?
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: [])
    # For GET requests (from emails), check if not BOT
    elsif request.get? && !verify_recaptcha
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: ['Please confirm you want to unsubscribe'])
    else
      @attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: params[:code]).call
    end

    respond_to do |format|
      format.html do
        render layout: 'team_order'
      end
      format.json do
        if @attendee_unsubscriber.success?
          render json: { success: true }
        else
          render json: { errors: @attendee_unsubscriber.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def unsubscribe_package
    # For HEAD requests (from prefetching), don't execute unsubscribe logic
    if request.head?
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: [])
    # For GET requests (from emails), check if not BOT
    elsif request.get? && !verify_recaptcha
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: ['Please confirm you want to unsubscribe'])
    else
      @attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: params[:code]).call
    end

    respond_to do |format|
      format.html do
        render layout: 'team_order'
      end
      format.json do
        if @attendee_unsubscriber.success?
          render json: { success: true }
        else
          render json: { errors: @attendee_unsubscriber.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def unsubscribe_open_package_order
    package_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: session_profile).call

    # For HEAD requests (from prefetching), don't execute unsubscribe logic
    if request.head?
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: [])
    # For GET requests (from emails), require confirmation parameter to prevent accidental unsubscribing
    elsif request.get? && params[:confirm] != 'true'
      @attendee_unsubscriber = OpenStruct.new(success?: false, errors: ['Please confirm you want to unsubscribe'])
    else
      @attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: package_attendee.try(:uniq_code)).call
    end

    respond_to do |format|
      format.html { render 'team_order_attendees/unsubscribe'}
      format.json do
        if @attendee_unsubscriber.success?
          render json: { success: true }
        else
          render json: { errors: @attendee_unsubscriber.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def show_package
    redirect_to team_order_attendee_package_url(code: params[:code])
  end

  def order
    clean_up_session_orders
    @supplier_order = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: (current_user.present? && current_user.admin?), profile: session_profile).call
    if @supplier_order.success?
      @supplier = @supplier_order.supplier

      menu_options = { team_order_menu: true }
      is_team_admin = @supplier_order.team_order_attendee.is_team_admin? || (session_profile.present? && @supplier_order.order.customer_profile == session_profile)
      menu_options[:within_budget] = is_team_admin ? nil : @supplier_order.order.team_order_budget
      menu_options[:selected_menu_sections] = @supplier_order.order.order_suppliers.first.selected_menu_sections
      menu_profile = @supplier_order.order.customer_profile.presence || session_profile

      @supplier_menu = Suppliers::FetchMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_options, profile: menu_profile).call
      if !@supplier_menu.success?
        error_message = I18n.t('team_order.errors.missing_order_attendee')
      end
    else
      error_message = I18n.t("team_order.errors.#{@supplier_order.error}")
    end

    if error_message.present?
      error_response = { errors: [error_message] }
      if (attendee = @supplier_order.team_order_attendee.presence) && !attendee.is_team_admin? && attendee.order.is_package_order?
        error_response[:package_url] = team_order_attendee_package_url(code: attendee.uniq_code)
      end
      render json: error_response.to_json, status: :unprocessable_entity
    else
      respond_to do |format|
        format.json
        format.html { render '/suppliers/show' } # can be removed after removing supplier menu html code
      end
    end
  end

private

  def fetch_team_order
    @team_order = Order.where(order_variant: %w[team_order recurring_team_order], unique_event_id: params[:event_id]).first if params[:event_id].present?
  end

  def event_attendee_params
    params.require(:event_attendee).permit(:first_name, :last_name, :email, :package_id, :level_id)
  end

  def team_order_params
    params.permit(:code, :event_id)
  end

  def redirect_to_next_app_team_order_attendee_order
    redirect_to next_app_team_order_attendee_order_url(team_order_params[:code]), status: 302 and return
  end

end
