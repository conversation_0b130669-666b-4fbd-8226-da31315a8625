class API::CustomOrdersController < ApplicationController

  before_action :ensure_yordar_admin, only: %i[fetch_suppliers supplier_menu]

  def fetch_suppliers
    @suppliers = SupplierProfile.where('company_name ilike ?', "%#{supplier_fetch_params[:query]}%").limit(15)
  end

  def supplier_menu
    @with_baseline_pricing = true
    @supplier = SupplierProfile.where(slug: params[:slug]).first
    @supplier_menu = Suppliers::FetchMenu.new(supplier: @supplier, suburb: nil, menu_options: default_menu_options, profile: session_profile).call
  end

private

  def supplier_fetch_params
    params.permit(:query)
  end

  def default_menu_options
    {
      is_admin: is_admin?
    }
  end
  
end
