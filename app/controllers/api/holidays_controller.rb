class API::HolidaysController < ApplicationController

  def index
    lister_options = {
      from_date: Time.zone.now.beginning_of_year,
      to_date: Time.zone.now.end_of_year,
      limit: nil,
    }.merge(list_params.to_h.deep_symbolize_keys)
    holidays = Holidays::List.new(options: lister_options).call
    @grouped_holidays = holidays.group_by do |holiday|
      [holiday.name, holiday.on_date]
    end
  end

private

  def list_params
    params.permit(:from_date, :to_date, :public_holidays_only, :state)
  end

end