class AccountingController < ApplicationController

  before_action :ensure_admin

  # User in Rails Admin Accounting tab
  def report
    csv_data = case params[:kind]
    when 'invoice'
      Admin::Reports::GenerateInvoiceReport.new(options: report_params).call
    else
      Admin::Reports::GenerateOrderReport.new(options: report_params, kind: params[:kind]).call
    end

    if csv_data.blank?
      flash[:warning] = 'No data found for given report parameters.'
      redirect_to prismic_root_path
    else
      send_data csv_data, filename: "#{params[:kind]}_report_#{Time.zone.now.to_s(:filename)}.csv"
    end
  end

private

  def report_params
    params.permit(:from, :to, :supplier_profile_id, :company_id, :customer_profile_id, :order_name)
  end

end