class SuppliersController < ApplicationController

  before_action :setup_suburb_from_name
  before_action :fetch_supplier, only: :show
  before_action :redirect_to_next_app_search, only: :index, if: :can_redirect_to_next_app?
  before_action :redirect_to_next_app_show, only: :show, if: :can_redirect_to_next_app?

  def index
    lister_options = [default_lister_options, supplier_list_params.to_h.symbolize_keys].inject(&:merge)
    suppliers = Suppliers::List.new(options: lister_options).call

    @supplier_minimums = Suppliers::GetMinimums.new(suppliers: suppliers.to_a, categories: lister_options[:category], category_group: lister_options[:category_group]).call

    supplier_sorter = Suppliers::GetSortedList.new(suppliers: suppliers, session_profile: session_profile, filter_options: lister_options).call

    @suppliers = supplier_sorter.sorted_suppliers
    @rate_card_supplier_ids = supplier_sorter.rate_card_supplier_ids
    @markup_override_supplier_ids = supplier_sorter.markup_override_supplier_ids
    @custom_menu_supplier_ids = supplier_sorter.custom_menu_supplier_ids
    @favourite_supplier_ids = supplier_sorter.favourite_supplier_ids

    respond_to do |format|
      format.json
      format.html { head :ok }
    end
  end

  def show
    @supplier_order = Orders::SetupForSupplierMenu.new(session_order: session_order, suburb: @suburb).call
    menu_options = [default_menu_options, menu_option_params.to_h.symbolize_keys].inject(&:merge)
    if @supplier.present? && @supplier.is_major_supplier?
      @supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_options, profile: session_profile, order: @supplier_order.order).call
    else
      @supplier_menu = Suppliers::FetchMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_options, profile: session_profile, order: @supplier_order.order).call
    end
    if @supplier_menu.success? && @supplier_order.success?
      @with_baseline_pricing = session_profile.present? && session_profile.is_a?(SupplierProfile) && session_profile == @supplier # supplier preview menu
      case
      when @supplier_order.order.new_record?
        clean_up_session_orders
      when @supplier_order.order != session_order
        session_order(@supplier_order.order.id)
      end
    else
      clean_up_session_orders
      errors = (@supplier_menu.errors + @supplier_order.errors).join('. ')
      respond_to do |format|
        format.html { redirect_to prismic_root_url, error: errors and return }
        format.json do
          if errors.include?('Please select an active Supplier')
            render json: { errors: errors }, status: :unprocessable_entity
          else
            render json: { error: 'not_found' }, status: :not_found
          end
        end
      end
    end
  end

private

  def redirect_to_next_app_search
    supplier_search_url = next_app_supplier_search_url(category_group: next_app_category_group(supplier_list_params[:category_group]), state: @suburb&.state, suburb: @suburb&.name&.gsub(/\s/, '-'), **supplier_list_params.except(:category_group))
    redirect_to supplier_search_url, status: 302 and return
  end

  def redirect_to_next_app_show
    return if @supplier.blank?

    other_params = menu_option_params.to_h.symbolize_keys.except(:slug)
    redirect_to next_app_supplier_show_url(@supplier.slug, **other_params), status: 302 and return
  end

  def fetch_supplier
    @suburb = Suburb.where(id: cookies[:yordar_suburb_id]).first
    @supplier = SupplierProfile.includes(:minimums, delivery_zones: :suburb).where(slug: (params[:id] || params[:slug])).first
  end

  def supplier_list_params
    filter_fields = %i[category_group search_keywords order_date searchable team_suppliers mealUUID]
    limit_fields = %i[offset limit refresh for_cache]
    params.permit(*filter_fields, *limit_fields, category: [], dietary: [], delivery: [], other: [])
  end

  def default_lister_options
    {
      visible_to: session_profile,
      searchable: true,
      suburb: @suburb,
      category_group: 'catering-services',
      for_react: is_react_app?,
      limit: (@suburb.blank? && params[:direct].blank? ? 6 : nil),
      is_admin: is_admin?,
    }
  end

  def setup_suburb_from_name
    @suburb = Suburbs::FetchForSearch.new(suburb_params: suburb_params, suburb_cookies: cookies, host: request&.host).call
    save_suburb_cookie(@suburb)
  end

  def suburb_params
    params.permit(:suburb, :state, :postcode, :street_address, :suburb_id)
  end

  def team_order_params
    params.permit(:code, :event_id)
  end

  def menu_option_params
    params.permit(:team_order_menu, :mealUUID, :category_group, :for_cache, selected_menu_sections: [])
  end

  def default_menu_options
    {
      is_admin: is_admin?
    }
  end

  def setup_cookies(order_cookies)
    return if order_cookies.blank?

    order_cookies.each do |name, value|
      cookies[name] = { value: value, domain: cookie_domain(host: request&.host) }
    end
  end

  def is_home_delivery?
    (params[:category_group].present? && params[:category_group] == 'home-deliveries') ||
      (session_order.present? && session_order.is_home_delivery?)
  end
  helper_method :is_home_delivery?

  def is_customer?
    session_profile.present? && session_profile.profile.is_customer?
  end
  helper_method :is_customer?

end
