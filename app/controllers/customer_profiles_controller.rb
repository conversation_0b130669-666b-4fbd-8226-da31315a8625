class CustomerProfilesController < ApplicationController
	include CloudinaryHelper

	before_action :authenticate_user!, except: :custom_order
	before_action :ensure_customer, except: :custom_order
	before_action :ensure_admin, only: :customer_settings
	respond_to :json
	layout 'customer_profiles'

	def show
		if !current_user.confirmed? && flash.blank?
			flash[:warning] = "<p>You have to confirm your email address before continuing.</p> <p><a href=#{resend_confirmation_path}>Resend confirmation</a></p>"
		end
	end

	def quotes; end

	def update
		customer_profile = CustomerProfile.find(params[:id])
		if customer_profile != session_profile
			redirect_to customer_account_and_billing_path, error: "You are logged in as '#{session_profile.name}' and were trying to change settings for '#{customer_profile.name}'" and return
		end

		customer_profile.update(customer_profile_params)

		if customer_billing_params.present?
			Customers::BillingDetails::Upsert.new(customer: customer_profile, detail_params: customer_billing_params).call
		end

		redirect_to customer_account_and_billing_path, success: 'Your account details have been updated'
	end

	def payment_options
		active_cards = session_profile.credit_cards.where(enabled: true, saved_for_future: true).order(created_at: :desc).distinct
	 	credit_cards = active_cards.where.not(stripe_token: nil)
  	if (nominated_eway_card = active_cards.where(auto_pay_invoice: true, stripe_token: nil).first.presence)
 			credit_cards = credit_cards.to_a.unshift(nominated_eway_card)
 		end
  	@credit_cards = credit_cards
	end

	def account_and_billing
		if session_profile.user&.unconfirmed_email.present?
			flash[:warning] = 'Your login email has changed and needs to be confirmed. Check your email for the confirmation link.'
		end
	end

	def customer_settings; end

	def meal_plans
		customer = session_profile
		@needs_billing_details = customer.billing_details.blank?
		@has_meal_plans = customer.meal_plans.where(archived_at: nil).present?
		@meal_plan = customer.meal_plans.where(archived_at: nil, uuid: params[:mealUUID]).first if params[:mealUUID].present?
	end

	def my_suppliers
		@customer ||= session_profile
		favourite_suppliers = @customer.favourite_suppliers.where(is_searchable: true)
		catering_suppliers = favourite_suppliers.joins(:menu_sections).merge(MenuSection.where(archived_at: nil).joins(:categories).where('categories.group = ?', 'catering-services')).distinct
		snacks_suppliers = favourite_suppliers.joins(:menu_sections).merge(MenuSection.where(archived_at: nil).joins(:categories).where('categories.group = ?', 'kitchen-supplies')).distinct
		recent_orders = @customer.orders.where('delivery_at > ?', (Time.zone.now - 1.month))
		recent_orders = recent_orders.where(status: %w[new amended confirmed delivered])
		recent_orders = recent_orders.includes(:supplier_profiles).where(supplier_profiles: { id: favourite_suppliers.map(&:id) })
		@recent_orders = recent_orders.order(delivery_at: :desc)
		@has_multiple_category_favourites = catering_suppliers.present? && snacks_suppliers.present?
		@shown_suppliers = case
		when (params[:category] == 'snacks' || catering_suppliers.blank?) && snacks_suppliers.present?
			{ suppliers: snacks_suppliers, category: 'snacks' }
		when catering_suppliers.present?
			{ suppliers: catering_suppliers, category: 'catering' }
		else
			nil
		end
	end

	def notification_preferences
		@notification_preferences = session_profile.notification_preferences
		render 'notification_preferences/index'
	end

	def request_admin_access
		if session_profile.company_team_admin?
			flash[:notice] = 'You are already setup as a Company Team Admin'
			redirect_to after_sign_in_path_for(current_user) and return
		end
	end

	def employee_surveys; end

	def resend_confirmation
		customer = current_user.profile.profileable
		if customer.present? && !customer.user.confirmed?
			Customers::Emails::SendWelcomeEmail.new(customer: customer).call
			flash[:success] = 'Confirmation email sent! Please check your email.'
		end
		redirect_to after_sign_in_path_for(current_user)
	end

private

	def customer_profile_params
		contact_fields = %i[contact_phone mobile]
		info_fields = %i[company_name role]
		user_fields = %i[firstname lastname email id secondary_email suburb_id]
		profile_fields = %i[id avatar]
		params.require(:customer_profile).permit(*contact_fields, *info_fields, user_attributes: user_fields, profile_attributes: profile_fields)
	end

	def customer_billing_params
		return {} if params[:billing_details].blank?

		info_fields = %i[name email address suburb_id phone]
		billing_fields = %i[frequency billing_day invoice_order_grouping]
		report_fields = %i[order_summaries summary_report invoice_spreadsheet]
		pricing_fields = %i[hide_delivery_pricing]
		params.require(:billing_details).permit(*info_fields, *billing_fields, *report_fields, *pricing_fields)
	end

end
