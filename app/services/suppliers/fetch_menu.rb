class Suppliers::FetchMenu

  def initialize(supplier:, menu_options: {}, suburb: nil, profile: nil, order: nil)
    @supplier = supplier
    @menu_options = [default_menu_options, menu_options.to_h.symbolize_keys].inject(&:merge)
    @suburb = suburb
    @profile = profile
    @order = order
    @result = Result.new(supplier: supplier)
  end

  def call
    if can_view_supplier?

      retrieve_menu_items
      if menu_options[:within_budget].present?
        retrieve_rate_cards
        retrieve_markup_override
        filter_menu_items_by_budget
      end
      retrieve_menu_section_grouped_items
      if !menu_options[:for_cache]
        fetch_favourites
        add_favourites_section_to_menu
        retrieve_rate_cards
        retrieve_markup_override
        retrieve_recent_orders
      end
    end
    result
  end

private

  attr_accessor :result, :menu_items
  attr_reader :supplier, :menu_options, :suburb, :profile, :order

  def can_view_supplier?
    case
    when supplier.blank?
      result.errors << 'Please select a valid Supplier'
    when !supplier.is_searchable? && (profile.blank? || profile != supplier)
      result.errors << 'Please select an active Supplier'
    when supplier.is_major_supplier?
      result.errors << 'Please select a non-major supplier'
    when !menu_options[:for_cache] && is_invisible_to_profile?
      result.errors << 'You do not have access to this Supplier'
    end
    result.errors.blank?
  end

  def item_lister_options
    order_type = is_team_order? ? 'team_order' : 'normal'
    order_by = menu_options[:favourites_only].present? ? 'customer_profile_menu_items.created_at DESC' : { weight: :asc }
    {
      suburb: suburb,
      show_visible: true,
      show_active: true,
      order_type: order_type,
      supplier: supplier,
      profile: profile,
      show_in_stock: supplier&.has_skus,
      for_cache: menu_options[:for_cache],
      ignore_custom_menu_sections: true,
      order_by: order_by,
      is_home_delivery: is_home_delivery?,
      query: menu_options[:query].presence,
      menu_section: menu_options[:menu_section].presence,
      show_favourites: menu_options[:favourites_only].presence,
      menu_sections: selected_menu_sections,
      mealUUID: menu_options[:mealUUID]
    }
  end

  def selected_menu_sections
    return [] if menu_options[:selected_menu_sections].blank?

    supplier.menu_sections.where(id: menu_options[:selected_menu_sections])
  end

  def retrieve_menu_items
    @menu_items = MenuItems::List.new(includes: [:menu_section, :serving_sizes, { menu_extra_sections: :menu_extras }], options: item_lister_options).call
  end

  def filter_menu_items_by_budget
    return if @menu_items.blank?

    @menu_items = menu_items.select do |menu_item|
      pricing_within_budget_for(menu_item: menu_item)
    end
  end

  def pricing_within_budget_for(menu_item:)
    return true if menu_options[:within_budget].blank? || menu_item.blank?

    serving_sizes = menu_item.serving_sizes.where(archived_at: nil).order(:weight)
    serving_sizes = serving_sizes.where(available_for_team_order: true) if is_team_order?
    rate_cards = result.grouped_rate_cards[menu_item.id]
    markup_override = result.markup_override
    min_rate_card_price = rate_cards.present? ? rate_cards.map{|card| card.price_inc_gst(gst_country: country_code) }.min : nil
    if serving_sizes.present?
      min_serving_price = serving_sizes.map{|serving_size| serving_size.markup_price(gst_country: country_code, override: markup_override) || 0 }.min
      min_price = [min_serving_price, min_rate_card_price].compact.min
    else
      min_item_price = menu_item.markup_price(gst_country: country_code, override: markup_override)
      min_price = [min_item_price, min_rate_card_price].compact.min
    end
    min_price.round(2) <= menu_options[:within_budget].to_f.round(2)
  end

  def section_lister_options
    {
      supplier: supplier,
      show_visible: true,
      show_active: true,
      profile: profile,
      ignore_custom: true,
      order_by: :weight,
      is_home_delivery: is_home_delivery?,
      within_budget: nil,
    }
  end

  def retrieve_menu_section_grouped_items
    section_grouped_items = {}
    case
    when menu_options[:menu_section].present?
     section_grouped_items[menu_options[:menu_section]] = menu_items
    when menu_options[:favourites_only].present?
      result.favourite_menu_items = section_grouped_items[favourite_menu_section] = menu_items
    when menu_options[:query].present?
      search_menu_section = MenuSection.search_menu_section(supplier: supplier, search_keywords: menu_options[:query])
      section_grouped_items[search_menu_section] = menu_items
    when menu_items.present?
      section_grouped_items = menu_items.group_by(&:menu_section)
    end
    result.section_grouped_menu_items = @section_grouped_menu_items = section_grouped_items.sort_by do |section, _|
      section_weight = section&.weight || 9999
      section&.companies.present? ? -1 * section_weight : section_weight
    end
  end

  def fetch_favourites
    favourites = case
    when menu_options[:favourites_only]
      [] # already got favourites
    when profile.blank? || !profile.is_a?(CustomerProfile)
      []
    when menu_items.blank? && menu_options[:query].blank?
      MenuItems::List.new(options: item_lister_options.merge({ show_favourites: true })).call
    when menu_items.present?
      CustomerProfileMenuItem.includes(:menu_item).where(customer_profile_id: profile, menu_item_id: menu_items.map(&:id)).order('customer_profile_menu_items.created_at DESC').map(&:menu_item)
    end
    result.favourite_menu_items = favourites
  end

  def add_favourites_section_to_menu
    return if result.favourite_menu_items.blank?
    return if @section_grouped_menu_items.blank?

    result.section_grouped_menu_items = @section_grouped_menu_items.unshift([favourite_menu_section, result.favourite_menu_items])
  end

  def retrieve_rate_cards
    rate_cards = case
    when menu_items.blank?
      {}
    when customer_company.present?
      RateCard.where(menu_item: menu_items.to_a, company: customer_company).group_by(&:menu_item_id)
    else
      {}
    end
    result.grouped_rate_cards = rate_cards
  end

  def retrieve_markup_override
    return if profile.blank? || !profile.is_a?(CustomerProfile)

    result.markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: profile, company: customer_company, required_override: :markup).call
  end

  def retrieve_recent_orders
    return if is_team_order? || !is_fetching_full_menu? || (order.present? && order.status != 'draft')

    result.recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: profile).call
  end

  def customer_company
    return nil if @_customer_company == false

    @_customer_company ||= profile.present? && profile.is_a?(CustomerProfile) && profile.company.presence
  end

  def favourite_menu_section
    MenuSection.favourite_menu_section(supplier: supplier)
  end

  def is_team_order?
    menu_options[:team_order_menu].present?
  end

  def country_code
    @country_code = order&.symbolized_country_code || suburb&.symbolized_country_code || :au
  end

  def is_home_delivery?
    (menu_options[:category_group].present? && menu_options[:category_group] == 'home-deliveries') ||
      order&.is_home_delivery?
  end

  def is_invisible_to_profile?
    (profile.present? && !supplier.visible_to(profile.id)) || (profile.blank? && supplier.customer_profiles.present?) || (supplier.admin_only && !menu_options[:is_admin])
  end

  def is_fetching_full_menu?
    %i[category_group query menu_section favourites_only].none?{|option| menu_options[option].present? }
  end

  def default_menu_options
    {
      category_group: nil,
      team_order_menu: false,
      query: nil,
      menu_section: nil,
      selected_menu_sections: [],
      favourites_only: false,
      for_cache: false,
      is_admin: false,
    }
  end

  class Result
    attr_accessor :supplier, :section_grouped_menu_items, :favourite_menu_items, :grouped_rate_cards, :markup_override, :recent_orders, :errors

    def initialize(supplier:)
      @supplier = supplier
      @section_grouped_menu_items = {}
      @favourite_menu_items = []
      @grouped_rate_cards = {}
      @markup_override = nil
      @recent_orders = []

      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end


