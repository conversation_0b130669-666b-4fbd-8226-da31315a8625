class Suppliers::Emails::SendOrderAmendedEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-order_changed'.freeze

  def initialize(supplier:, order:, detail_changes: [], item_changes: [])
    @supplier = supplier
    @order = order
    @detail_changes = detail_changes
    @item_changes = item_changes
    @attachments = []
    @result = Result.new
  end

  def call
    begin
      generate_documents
      send_email
    rescue => exception
      error_message = "Failed to send new order email to supplier #{supplier.id} - order ##{order.id} - #{exception.message}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_id: order.id })
    end
    result
  end

private

  attr_reader :supplier, :order, :detail_changes, :item_changes, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      mark_supplier_as_notified
      Rails.logger.info "Order amended email sent to supplier #{supplier.id} - order ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    subject = "YORDAR: An order was changed - ##{order.id}"
    subject += " (Ver.#{@details_document.version})" if @details_document.present?
    subject
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    change_type = case
    when !order.is_recurrent?
      'one-off'
    when order.template_id != order.id || (order.template_id == order.id && order.old_delivery_at.present?)
      'one off'
    else
      'permanent'
    end
    {
      firstname: supplier.email_salutation,
      has_commission: supplier.commission_rate > 0.0,

      order: deep_struct(order_data),
      change_type: change_type,

      detail_changes: detail_changes,
      item_changes: item_changes,

      header_color: :cream
    }
  end

  def order_data
    {
      id: order.id,
      delivery_details: delivery_details,
      date: (order.delivery_at&.to_s(:full) || ''),
      day: (order.template_delivery_at&.to_s(:weekday) || ''),
      customer_name: (order.customer_profile&.customer_or_company_name || ''),
      is_event_order: order.is_event_order?,
      is_contactless_delivery: supplier.provides_contactless_delivery && order.is_contactless_delivery?,
      has_no_orderlines: supplier_order_lines.blank?,

      pdf_url: @details_document&.url,
      pdf_version: @details_document&.version,
      avery_labels_url: @avery_document&.url,
      flex_url: @flex_document&.url,
      view_url: url_helper.supplier_order_show_url(order, host: app_host),
      confirm_url: order_urls(mode: 'accepted'),
      reject_url: order_urls(mode: 'rejected'),
    }
  end

  def supplier_order_lines
    return @_supplier_order_lines if !@_supplier_order_lines.nil?

    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?
    }
    @_supplier_order_lines = OrderLines::List.new(options: lister_options).call
  end

  # Order changes - we need to treat the delivery at field differently as we want to show the before and after
  def delivery_details
    details = { was: nil, new: nil }
    prev_version = order.versions.order(:id).where('created_at > ?', order.suppliers_notified_at).where(event: 'update').first
    if prev_version.present?
      prev_record = prev_version.reify
      details = { was: prev_record.send(:delivery_at)&.to_s(:full_date), new: order.send(:delivery_at)&.to_s(:full_date) }
      if details[:was] == details[:new]
        details = { was: nil, new: nil }
      end
    end
    details
  end

  def order_urls(mode:)
    path = url_helper.order_confirm_or_reject_url(order_id: order.id, profile_type: 'supplier', profile_id: supplier.id, mode: mode, hashed_value: md5_hash, host: app_host)
    url_shortner = Shortener::ShortenedUrl.generate(path)
    url_helper.shortened_url(url_shortner.unique_key, host: app_host)
  end

  def md5_hash
    @_md5_hash ||= Digest::MD5.hexdigest(order.id.to_s + supplier.id.to_s + yordar_credentials(:random_salt))
  end

  def generate_documents
    return if supplier_order_lines.blank?

    generate_order_detail_document
    generate_delivery_detail_document
    generate_avery_label_csv if order.is_team_order?
    generate_flex_json if supplier.uses_flex_catering
  end

  def generate_order_detail_document
    @details_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref).call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def generate_delivery_detail_document
    delivery_details_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref, variation: 'delivery_docket').call
    if delivery_details_document.present?
      attachments << delivery_details_document
    end
  end

  def generate_avery_label_csv
    @avery_document = Documents::Generate::TeamOrderAveryLabels.new(team_order: order, supplier: supplier, reference: "team-order-#{order.id}-#{order.version_ref}").call
    if @avery_document.present?
      attachments << @avery_document
    end
  end

  def generate_flex_json
    @flex_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref, variation: 'json').call
    if @flex_document.present?
      attachments << @flex_document
    end
  end

  def mark_supplier_as_notified
    Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.version_ref}"
  end

end

