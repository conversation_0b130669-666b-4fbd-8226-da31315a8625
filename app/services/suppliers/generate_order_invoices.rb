# Goes 3 things
# Generates invoices per supplier for their orders within frequency time frame
# Send generated Invoice and RGI document to supplier via email
# Supplier Invoice are uploaedd to Xero in a different task
class Suppliers::GenerateOrderInvoices

  def initialize(frequency: 'weekly', time: Time.zone.now, notify_supplier: false)
    @frequency = frequency
    @time = time
    @notify_supplier = notify_supplier
    @result = Result.new
  end

  def call
    invoicable_supplier_orders.each do |supplier, orders|
      next if orders.blank?

      generate_invoice_for(supplier: supplier, supplier_orders: orders)
    end
    result
  end

private

  attr_reader :frequency, :time, :notify_supplier, :result

  def invoicable_supplier_orders
    supplier_orders = {}
    invoicable_order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
      supplier_orders[supplier] = supplier_orders_from(supplier: supplier, supplier_order_lines: supplier_order_lines)
    end
    supplier_orders
  end

  def supplier_orders_from(supplier:, supplier_order_lines:)
    supplier_orders = supplier_order_lines.map(&:order).uniq
    supplier_orders = supplier_orders.select do |order|
      order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
      order_supplier.blank? || order_supplier.supplier_invoice_id.blank?
    end
    supplier_orders.sort_by do |order|
      order.delivery_at.to_i + order.id
    end
  end

  def generate_invoice_for(supplier:, supplier_orders:)
    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: supplier_orders, invoice_dates: delivery_times, notify_supplier: notify_supplier).call
    if invoice_generator.success?
      supplier_invoice = invoice_generator.generated_invoice
      result.invoices << supplier_invoice
    else
      result.errors += invoice_generator.errors
    end
  end

  def delivery_times
    @_delivery_times ||= case
    when frequency == 'weekly' && time.beginning_of_day == time.beginning_of_month && time.wday != 1 # non Monday start of month
      current_week_start = time.beginning_of_week
      {
        from: current_week_start,
        to: current_week_start.end_of_month
      }
    when frequency == 'weekly' && previous_week.beginning_of_week.month != previous_week.end_of_week.month && time.wday == 1 # first monday of month
      previous_week_end = previous_week.end_of_week
      {
        from: previous_week_end.beginning_of_month,
        to: previous_week_end,
      }
    when frequency == 'weekly'
      {
        from: previous_week.beginning_of_week,
        to: previous_week.end_of_week,
      }
    when frequency == 'fortnightly'
      {
        from: previous_fortnight.beginning_of_week,
        to: previous_week.end_of_week,
      }
    when frequency == 'monthly'
      {
        from: previous_month.beginning_of_month,
        to: previous_month.end_of_month,
      }
    end
  end

  def invoicable_order_lines
    order_lines = OrderLine.where(sent_as_rgi_to_xero: [nil, false])
    order_lines = order_lines.joins(:order)
    order_lines = order_lines.where(orders: { status: %w[delivered confirmed], split_order_id: nil })
    order_lines = order_lines.where(orders: { delivery_at: [delivery_times[:from]..delivery_times[:to]] })
    order_lines = order_lines.joins(supplier_profile: :supplier_flags)
    order_lines = order_lines.where(supplier_flags: { billing_frequency: frequency })
    order_lines.includes(:supplier_profile, :order)
  end

  def previous_week
    @_previous_week ||= time - 1.week
  end

  def previous_fortnight
    @_previous_fortnight ||= time - 2.weeks
  end

  def previous_month
    @_previous_month ||= time - 1.month
  end

  class Result
    attr_accessor :invoices, :errors

    def initialize
      @invoices = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
