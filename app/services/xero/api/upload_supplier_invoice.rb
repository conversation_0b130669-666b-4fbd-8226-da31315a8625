class Xero::API::UploadSupplierInvoice < Xero::API::Base

  def initialize(invoice:)
    @invoice = invoice
    @result = Result.new
  end

  def call
    if can_upload? && is_valid_contact?
      push_purchase_order_to_xero
    end
    result
  end

private

  attr_reader :invoice, :contact, :xero_invoice, :result

  def can_upload?
    case
    when invoice.blank?
      result.errors << 'Cannot push a missing Supplier Invoice'
    when supplier.blank?
      result.errors << 'Cannot push purchases without a supplier'
    when orders.blank?
      result.errors << 'Cannot push missing purchases'
    when invoice.number.blank?
      result.errors << 'Cannot push without an Invoice number'
    end
    result.errors.blank?
  end

  def is_valid_contact?
    contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
    if contact_uploader.success?
      result.contact = @contact = contact_uploader.contact
    else
      result.errors << "Failed to save/update supplier #{supplier.id}"
      result.errors += contact_uploader.errors
    end
    result.errors.blank?
  end

  def push_purchase_order_to_xero
    begin
      @xero_invoice = xero_client.Invoice.build(
        type: SUPPLIER_INVOICE_TYPE,
        contact: contact,
        invoice_number: invoice.number,
        date: invoice.to_at,
        due_date: invoice.due_at,
        url: invoice_path
      )

      orders.each do |order|
        add_line_item_for(order: order)
      end

      add_delivery_fees
      save_xero_invoice
    rescue => exception
      result.errors << "Could not upload purchase orders for supplier #{supplier.name} (##{supplier.id}) to Xero"
      result.errors << exception.inspect
    end
  end

  def add_line_item_for(order:)
    totals = totals_for(order: order)

    order_lines = supplier_order_lines_for(order: order)
    is_gst_free = order_lines.map(&:is_gst_free).uniq
    all_gst_free_items = is_gst_free.size == 1 && is_gst_free.first

    line_item = xero_invoice.add_line_item(
      description: "#{order.name} - ##{order.id}",
      quantity: 1,
      tax_amount: totals.gst.round(2),
      unit_amount: totals.subtotal.round(2),
      account_code: (all_gst_free_items ? gst_free_account : non_gst_free_account)
    )
    # Override the tax type
    if override_account_code.present?
      line_item.account_code = override_account_code
      line_item.tax_type = all_gst_free_items ? 'EXEMPTEXPENSES' : 'INPUT' # INPUT: GST On Expenses; EXEMPTEXPENSES: GST Free Expenses
    end

    if totals.topup.present? && totals.topup > 0
      xero_invoice.add_line_item(description: "Topup - ##{order.id}", quantity: 1, unit_amount: totals.topup)
    end
  end

  def add_delivery_fees
    cumulative_delivery_fee = orders.map do |order|
      totals_for(order: order)&.delivery
    end.reject(&:blank?).sum
    xero_invoice.add_line_item(description: 'Delivery fee', quantity: 1, unit_amount: cumulative_delivery_fee.to_f.round(2), account_code: yordar_credentials(:xero, :freight_code))
  end

  def save_xero_invoice
    if xero_invoice.save
      mark_order_lines_as_pushed
      invoice.update(pushed_to_xero: true)
      result.xero_invoice = xero_invoice
    else
      result.errors << 'We were unable to save the Invoice'
      result.errors += xero_invoice.errors
    end
  end

  def mark_order_lines_as_pushed
    orders.each do |order|
      order_lines = supplier_order_lines_for(order: order)
      order_lines.update_all(sent_as_rgi_to_xero: true)
    end
  end

  def supplier_order_lines_for(order:)
    @_supplier_order_lines ||= {}
    return @_supplier_order_lines[order] if @_supplier_order_lines[order].present?

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
      supplier: supplier
    }
    @_supplier_order_lines[order] = OrderLines::List.new(options: lister_options, includes: %i[supplier_profile order]).call
  end

  def totals_for(order:)
    @_supplier_totals ||= {}
    @_supplier_totals[order] ||= Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, order_lines: supplier_order_lines_for(order: order), save_totals: true).call
  end

  def override_account_code
    @_override_account_code ||= SUPPLIER_ACCOUNT_CODES[supplier.user.id.to_s]
  end

  def gst_free_account
    @_gst_free_account ||= yordar_credentials(:xero, :supplier_gst_free)
  end

  def non_gst_free_account
    @_non_gst_free_account ||= yordar_credentials(:xero, :supplier_non_gst_free)
  end

  def supplier
    @_supplier ||= invoice.supplier_profile
  end

  def orders
    @_orders = invoice.orders
  end

  def invoice_path
    @_invoice_path ||= begin
      rgi_document = invoice.documents.where(kind: 'recipient_generated_invoice').order(version: :desc).first
      rgi_document.present? ? rgi_document.url : nil
    end
  end

  class Result
    attr_accessor :xero_invoice, :contact, :errors

    def initialize
      @xero_invoice = nil
      @contact = nil
      @errors = []
    end

    def success?
      errors.blank? && xero_invoice.present?
    end
  end

end
