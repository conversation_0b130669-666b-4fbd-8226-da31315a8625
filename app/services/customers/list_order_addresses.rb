class Customers::ListOrderAddresses

  ORDER_THRESHOLD = 100

  def initialize(customer:, suburb: nil, time: Time.zone.now)
    @customer = customer
    @suburb = suburb
    @time = time
    @addresses = []
  end

  def call
    fetch_recent_delivery_addresses_from_orders
    addresses.uniq{|address| address.label.downcase }
  end

private

  attr_reader :customer, :suburb, :time, :addresses

  def fetch_recent_delivery_addresses_from_orders
    recent_orders.each do |recent_order|
      @addresses << Orders::GetSanitizedAddress.new(order: recent_order).call
    end
  end

  def recent_orders
    orders = customer_orders.where('delivery_at >= ?', (time - 1.years))
    orders = customer_orders.where('delivery_at >= ?', (time - 2.years)) if orders.blank?
    orders.select(:id, :delivery_address, :delivery_address_level, :delivery_suburb_id, :delivery_instruction)
  end

  def customer_orders
    orders = Order.where(customer_profile: customer)
    orders = orders.where.not(delivery_address: [nil, ''])
    orders = orders.where(delivery_suburb: suburb) if suburb.present?
    orders.order(delivery_at: :desc).limit(ORDER_THRESHOLD)
  end

end
