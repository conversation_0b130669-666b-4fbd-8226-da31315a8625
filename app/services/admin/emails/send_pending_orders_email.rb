class Admin::Emails::SendPendingOrdersEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'yordar-pending_orders'.freeze

  def initialize(pending_orders:, delivery_on:)
    @pending_orders = pending_orders
    @delivery_on = delivery_on
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = 'Failed to send pending orders email to admin'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { pending_orders: pending_orders.map(&:id) })
    end
  end

private

  attr_reader :pending_orders, :delivery_on

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info 'Sent pending orders email to admin'
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :admin_email)
  end

  def email_subject
    "YORDAR: Pending Orders x#{pending_orders.size}"
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      delivery_date: delivery_on.to_s(:full_date),
      pending_orders: deep_struct(pending_orders_data),
      has_quote_orders: has_quote_orders?,
    }
  end

  def pending_orders_data
    orders = pending_orders.includes(:customer_profile)
    orders.map do |order|
      case order.order_variant
      when 'event_order'
        order_link = url_helper.custom_orders_admin_url(order_name: order.id, host: app_host)
        order_type = 'custom order'
      else
        order_link = url_helper.orders_admin_url(order_name: order.id, host: app_host)
        order_type = 'quoted order'
      end
      {
        id: order.id,
        link: order_link,
        type: order_type,
        total: number_to_currency(order.customer_total, precission: 2),
        delivery_time: (order.delivery_at.present? ? order.delivery_at.to_s(:time_only) : 'unknown'),
        customer_name: (order.customer_profile.present? ? order.customer_profile.name : 'unknown'),
        suppliers: suppliers_data_for(order),
      }
    end
  end

  def suppliers_data_for(order)
    order.supplier_profiles.map do |supplier|
      {
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
      }
    end
  end

  def has_quote_orders?
    pending_orders.detect{|order| order.status == 'quoted' }.present?
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end
