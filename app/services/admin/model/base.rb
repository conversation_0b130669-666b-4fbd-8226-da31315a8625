class Admin::Model::Base

  def initialize; end

  def model
    @model ||= self.class.name.sub('Admin::Model::', '').constantize
  end

  def list_fields
    self.class::ADMINABLE_FIELDS
  end

  def show_fields
    []
  end

  def new_fields
    self.class::ADMINABLE_FIELDS
  end

  def edit_fields
    self.class::ADMINABLE_FIELDS
  end

  def field_config
    self.class::ADMINABLE_FIELDS.map do |field|
      field_attr = model.column_for_attribute(field)
      field_type = case
      when field_attr.respond_to?('array') && field_attr.array
        'array'
      else
        field_attr.type
      end
      field_options = send("#{field}_options").presence rescue []
      field_type = 'select' if field_options.present?
      field_type = 'jsonb' if field == 'contact_info'
      OpenStruct.new(
        name: field,
        label: field.titleize,
        type: field_type,
        options: field_options,
        in_list: list_fields.include?(field),
        in_edit: edit_fields.include?(field),
        in_new: new_fields.include?(field),
        in_show: show_fields.include?(field)
      )
    end
  end

end