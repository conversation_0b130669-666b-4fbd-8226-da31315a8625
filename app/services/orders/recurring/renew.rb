class Orders::Recurring::Renew
  class RenewalException < StandardError
    def initialize(message = '')
        super(message)
    end
  end

  def initialize(order:)
    @order = order
    @result = Result.new(order: order)
  end

  def call
    is_valid_order_to_renew?

    result.template = template_order

    create_new_order

    if new_order.present? && new_order.valid?
      begin
        # using update column function to by-pass validation
        order.update_column(:renewed_to_id, new_order.id) # the current order is renewed to the new order
        populate_renewed_order
        sanitize_renewed_order
        sync_promotion_for_order
        sync_dear_order if new_order.status == 'confirmed'
        generate_supplier_documents
      rescue => exception
        Rails.logger.error "Failed to renew a recurring order ##{order.id}"
        Rails.logger.error exception.inspect
        Rails.logger.error exception.backtrace.join('\n')
        if Rails.env.production?
          Raven.capture_exception(exception,
            message: "Failed to renew a recurring order ##{order.id}",
            extra: { order_id: order.if },
            transaction: 'Orders::Recurring::Renew'
          )
        end
      end
    end

    result
  end

private

  attr_accessor :order, :new_order, :result

  def template_order
    @_template_order ||= Order.where(id: order.template_id).first
  end

  def is_valid_order_to_renew?
    case
    when order.blank?
      raise RenewalException.new('Cannot renew a non-existant order')
    when order.recurrent_id.blank?
      raise RenewalException.new('Cannot renew a one-off order (recurrent_id: nil)')
    when order.template_id.blank?
      raise RenewalException.new('Cannot renew an order without a template (template_id: nil)')
    when order.renewed_to_id.present?
      raise RenewalException.new("This order was already renewed (renewed_to_id: #{order.renewed_to_id})")
    when template_order.status == 'cancelled' && template_order.order_type != 'one-off'
      raise RenewalException.new("Cannot renew order with template order being cancelled permanently (order_id: '#{order.id}', template_order_id: '#{template_order.id}')")
    when %w[confirmed amended paused skipped].exclude?(order.status) && (order.status != 'cancelled' || order.order_type != 'one-off')
      raise RenewalException.new("Cannot renew unconfirmed order (status: '#{order.status}')")
    when template_order.pattern.blank?
      raise RenewalException.new('Cannot renew an order with no pattern (pattern: nil)')
    end
  end

  def create_new_order
    result.renewed_order = @new_order = template_order.dup

    new_order.order_type = 'recurrent'
    new_order.template_id = order.template_id # order.template_id should always be the same as the template_order.template_id, but template_order may not have a template_id because of the cancellation
    new_order.renewed_from_id = order.id
    new_order.renewed_to_id = nil # the new order was never renewed
    new_order.status = 'draft' # all renewed orders start off as draft + overrides template status + are confirmed after adding order lines

    new_order.invoice_id = nil # new order cannot already be invoiced
    new_order.payment_status = 'unpaid' # there's no way a new order (in the future) has been paid for already

    new_order.loading_dock_id = nil # new order gets it's own loading dock info

    new_order.delivery_at = new_order_delivery_at
    new_order.old_delivery_at = nil # need to reset old_delivery_at which was used for public holiday emails

    new_order.uuid = SecureRandom.uuid # save a new uuid for the renewed order

    # reset to pay on account if customer can pay on account or is nominated card (auto pay invoice card)
    new_order.credit_card_id = pay_on_account_card.id if new_order.credit_card.present? && (new_order.customer_profile.can_pay_on_account? || new_order.credit_card.auto_pay_invoice?)

    if new_order.save
      result.renewed_order = new_order.reload
    else
      result.errors += new_order.errors.full_messages
    end
  end

  def populate_renewed_order
    create_order_lines
    sync_supplier_overrides
    calculate_order_totals
  end

  def sanitize_renewed_order
    new_order.update_column(:suppliers_notified_at, Time.zone.now) # mark order as notified
    new_status = case
    when order.status == 'paused'
      'paused'
    else
      'confirmed' # renewed orders end up as confirmed
    end
    new_order.update(status: new_status)
  end

  def sync_promotion_for_order
    Promotions::SyncWithOrder.new(order: new_order, customer: customer).call
  end

  def pay_on_account_card
    @_pay_on_account_card ||= CreditCard.where(pay_on_account: true).first
  end

  def customer
    @_customer ||= order.customer_profile
  end

  def order_lines
    @_order_lines ||= template_order.order_lines.includes(menu_item: :serving_sizes)
  end

  def mismatched_order_lines
    @_mismatched_order_lines ||= Orders::FetchMismatchedOrderLines.new(order: order, order_lines: order_lines).call
  end

  def create_order_lines
    return if order_lines.blank?

    location_grouped_order_lines = order_lines.group_by(&:location)
    location_grouped_order_lines.each do |location, location_order_lines|

      new_location = create_new_location(location: location)
      next if new_location.blank?

      order_params = { location_id: new_location.id }

      order_lines_params = location_order_lines.map do |order_line|
        found_mismatched_order_line = mismatched_order_lines.present? && mismatched_order_lines.detect{|mismatched_order_line| mismatched_order_line.id == order_line.id }
        order_line = found_mismatched_order_line.presence || order_line
        {
          item_id: order_line.menu_item_id,
          serving_size_id: order_line.serving_size_id,
          selected_menu_extra_ids: order_line.selected_menu_extras,
          note: order_line.note,
          quantity: order_line.quantity,
        }
      end
      multiple_creator = OrderLines::CreateMultiple.new(order: new_order, order_params: order_params, order_lines_params: order_lines_params).call
      if !multiple_creator.success?
        result.errors += multiple_creator.errors
      end
    end
    new_order.order_lines.update_all(status: 'accepted')
  end

  def create_new_location(location:)
    location_upserter = Locations::Upsert.new(order: new_order, location_params: { details: location.details }).call
    if location_upserter.success?
      location_upserter.location
    else
      result.errors += location_upserter.errors
      nil
    end
  end

  def sync_supplier_overrides
    overriden_order_suppliers = order.order_suppliers.where(supplier_profile: order.supplier_profiles).where.not(delivery_fee_override: nil)
    return if overriden_order_suppliers.blank?

    overriden_order_suppliers.each do |order_supplier|
      new_order_supplier = new_order.order_suppliers.where(supplier_profile: order_supplier.supplier_profile).first_or_create
      new_order_supplier.update(delivery_fee_override: order_supplier.delivery_fee_override)
    end
  end

  def calculate_order_totals
    new_order.reload
    Orders::CalculateCustomerTotals.new(order: new_order, save_totals: true).call
    new_order.supplier_profiles.each do |supplier|
      Orders::CalculateSupplierTotals.new(order: new_order, supplier: supplier, save_totals: true).call
    end
  end

  def new_order_delivery_at
    @_new_order_delivery_at ||= (order.old_delivery_at || order.delivery_at) + eval(new_order.pattern)
  end

  def sync_dear_order
    if (dear_suppliers = new_order.dear_suppliers.presence)
      dear_suppliers.each do |supplier|
        Dear::SyncOrder.new(order: order, supplier: supplier).delay(queue: :data_integrity).call
      end
    end
  end

  def generate_supplier_documents
    new_order.supplier_profiles.each do |supplier|
      Documents::Generate::SupplierOrderDetails.new(order: new_order, supplier: supplier, version_override: 1).call
      Documents::Generate::SupplierOrderDetails.new(order: new_order, supplier: supplier, variation: 'delivery_docket', version_override: 1).call
      Documents::Generate::SupplierOrderDetails.new(order: new_order, supplier: supplier, variation: 'json', version_override: 1).call if supplier.uses_flex_catering
    end
  end

  class Result
    attr_accessor :order, :template, :renewed_order, :errors

    def initialize(order:)
      @order = order
      @template = nil
      @renewed_order = nil
      @errors = []
    end

    def success?
      renewed_order.present? && errors.blank?
    end
  end
end
