class Orders::Cancel

  VALID_CANCEL_MODES = %w[void one-off subsequent on-hold related]

  def initialize(order:, mode: , is_admin: false)
    @order = order
    @cancel_mode = mode
    @is_admin = is_admin
    @orignially_quoted = (order.status == 'quoted').dup
    @result = Result.new(order: order)
  end

  def call
    return result if !can_cancel?

    cancelled_orders = case cancel_mode
    when 'void'
      refund_on_hold_charges
      order.update(status: 'voided', order_type: 'one-off')
      [order]
    when 'one-off'
      refund_on_hold_charges
      order.update(status: 'cancelled', order_type: 'one-off')
      [order]
    when 'subsequent', 'on-hold'
      subsequent_orders.each do |subsequent_order|
        subsequent_order.update(template_id: order.id)
      end
      to_be_cancelled_orders = subsequent_orders.select{|order| order.order_type != 'one-off' && order.status != 'cancelled'}
      new_status = cancel_mode == 'on-hold' ? 'paused' : 'cancelled'
      to_be_cancelled_orders.each do |cancel_order|
        cancel_order.update(status: new_status)
      end
      to_be_cancelled_orders
    when 'related'
      subsequent_related_orders.each do |cancel_order|
        cancel_order.update(status: 'cancelled')
      end
      subsequent_related_orders
    end
    result.cancelled_orders = cancelled_orders
    notify_suppliers_of(cancelled_orders)
    log_event
    result
  end

private

  attr_reader :order, :cancel_mode, :orignially_quoted, :is_admin, :result

  def can_cancel?
    case 
    when cancel_mode.blank? || VALID_CANCEL_MODES.exclude?(cancel_mode)
      result.errors << 'Need a valid cancel mode'
    when order.woolworths_order.present? && !is_admin
      result.errors << "Only Yordar Admins can cancel a Woolworths Order. Please get in touch with Yordar Admin via #{yordar_credentials(:yordar, :orders_email)}"
    end
    result.errors.blank?
  end

  def subsequent_orders
    @_subsequent_orders ||= Order.where(template_id: order.template_id).where('delivery_at >= ?', order.delivery_at)
  end

  def subsequent_related_orders
    @_related_orders ||= Order.where(recurrent_id: order.recurrent_id).where('delivery_at >= ?', order.delivery_at)
  end

  def refund_on_hold_charges
    return if order.order_type != 'one-off' || order.credit_card.blank? || order.credit_card.pay_on_account?

    Orders::OnHoldCharges::Refund.new(order: order).delay(queue: :data_integrity).call
  end

  def notify_suppliers_of(cancelled_orders)
    return if cancelled_orders.blank? || order.status == 'voided'

    suppliers_to_notify = order.is_team_order? ? order.team_supplier_profiles : order.supplier_profiles

    if orignially_quoted
      supplier = suppliers_to_notify.sample
      cancelled_orders.each do |order|
        Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier, notified_at: order.created_at).call
      end
    else
      suppliers_to_notify.each do |notifable_supplier|
        Suppliers::Emails::SendOrderCancelledEmail.new(mode: cancel_mode, supplier: notifable_supplier, orders: cancelled_orders.sort_by(&:id)).delay(queue: :notifications).call
      end
    end
  end

  def log_event
    return if %w[one-off subsequent related].exclude?(cancel_mode)

    event = %w[subsequent related].include?(cancel_mode) ? 'order-cancelled-permanently' : 'order-cancelled'
    EventLogs::Create.new(event_object: order, event: event, severity: 'warning').delay(queue: :notifications).call
  end

  class Result
    attr_accessor :order, :cancelled_orders, :errors

    def initialize(order:)
      @order = order
      @cancelled_orders = []
      @errors = []
    end

    def success?
      errors.blank? && %w[paused cancelled voided].include?(order.reload.status) && cancelled_orders.present?
    end
  end

end
