class Orders::Emails::SendConfirmationCheckEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-order_confirmation_check'.freeze

  def initialize(auto_confirmed_orders:)
    @auto_confirmed_orders = auto_confirmed_orders
  end

  def call
    return if auto_confirmed_orders_data.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send daily confirmation check email to <PERSON>rda<PERSON> Admin #{email_recipients}"
      log_errors(exception: exception, message: error_message, sentry: true)
    end
  end

private

  attr_reader :auto_confirmed_orders

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Daily order confirmation check email sent to Yordar Admin #{email_recipients}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :orders_email)
  end

  def email_subject
    check_date = Time.zone.now.to_s(:date)
    "YORDAR: Daily order confirmation check - #{check_date}"
  end

  def email_options
    {
      fk_id: 0,
      ref: email_ref,
    }
  end

  def email_variables
    {
      auto_confirmed_orders: deep_struct(auto_confirmed_orders_data)
    }
  end

  def auto_confirmed_orders_data
    return @_auto_confirmed_orders_data if !@_auto_confirmed_orders_data.nil?

    orders_data = auto_confirmed_orders.map do |order|
      is_catering_order = order.major_category_id.present? && order.major_category.group == 'catering-services'
      is_woolworths_order = order.woolworths_order.present?
      next if !is_catering_order && !is_woolworths_order

      order_data_for(order)
    end
    @_auto_confirmed_orders_data = orders_data.compact
  end

  def order_data_for(order)
    {
      id: order.id,
      name: order.name,
      customer_name: order.customer_profile.customer_name,
      link: url_helper.orders_admin_url(order_name: order.id, host: app_host),
      suppliers: suppliers_data_for_order(order)
    }
  end

  def suppliers_data_for_order(order)
    order.supplier_profiles.map do |supplier|
      {
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
        phone: supplier.phone || supplier.mobile
      }
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{Time.zone.now.to_s(:date)}"
  end
end

