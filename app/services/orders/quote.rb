class Orders::Quote

  def initialize(order:, order_params: {}, quote_params: {}, customer: nil)
    @order = order
    @order_params = order_params
    @quote_params = quote_params
    @customer = order&.customer_profile || customer
    @result = Result.new
  end

  def call
    if can_quote?
      order_updater = Orders::Update.new(order: order, order_params: sanitized_params, profile: customer).call
      if order_updater.success?
        sync_promotions
        update_customer_quote if order.customer_quote_id.present?
        generate_quote_document
        send_quote_emails
        if is_woolworths_order?
          detach_woolworths_account
        else
          log_event
        end
        result.order = order_updater.order
      else
        result.errors += order_updater.errors
      end
    end
    result
  end

private

  attr_accessor :order, :customer, :quote_params, :order_params, :result

  def can_quote?
    case
    when %w[quoted draft].exclude?(order.status)
      result.errors << 'Cannot quote a non-draft order'
    when order.is_recurrent?
      result.errors << 'Cannot quote a recurring order'
    when order.order_lines.blank?
      result.errors << 'Cannot quote an empty order'
    end
    result.errors.blank?
  end

  def sanitized_params
    [
      default_submission_params,
      purchase_order_params,
      order_params.except(:po_number, :cpo_id, :gst_free_cpo_id)
    ].inject(&:merge)
  end

  def default_submission_params
    {
      status: order_status,
      customer_profile: (order.customer_profile || customer),
    }
  end

  def purchase_order_params
    {
      customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:cpo_id]).call,
      gst_free_customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:gst_free_cpo_id]).call,
    }
  end

  def sync_promotions
    Promotions::SyncWithOrder.new(order: order, customer: customer).call
  end

  def update_customer_quote
    customer_quote = order.customer_quote
    return if customer_quote.blank? || %w[quoted accepted].include?(order.customer_quote.status)

    order.customer_quote.update(status: 'quoted')
  end

  def send_quote_emails
    return if %w[save-quote save-for-later].include?(quote_params[:mode]) || quote_params[:quote_emails].blank?

    quote_emails = quote_params[:quote_emails]
    split_quote_emails = quote_emails.split(';').map(&:strip).compact
    customer_email, non_customer_emails = split_quote_emails.partition{|quote_email| customer.email_recipient.include?(quote_email) }

    Customers::Emails::SendOrderQuoteEmail.new(order: order, customer: customer, document: @quote_document, quote_message: quote_params[:quote_message]).delay(queue: :notifications).call if customer_email.present?
    Customers::Emails::SendOrderQuoteEmail.new(order: order, customer: customer, document: @quote_document, quote_emails: non_customer_emails.join(';'), quote_message: quote_params[:quote_message]).delay(queue: :notifications).call if non_customer_emails.present?
  end

  def generate_quote_document
    return if quote_params[:mode] == 'save-for-later' && order.is_event_order?

    document_reference = "#{Customers::Emails::SendOrderQuoteEmail::EMAIL_TEMPLATE}-#{order.id}-#{Time.zone.now.to_s(:date)}"
    @quote_document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: document_reference, variation: 'quote').call
  end

  def log_event
    event_type = case
    when order.is_event_order? && order.status == 'draft'
      'custom-order-saved-as-draft'
    when order.is_event_order?
      'new-custom-order-quoted'
    else
      'new-order-quoted'
    end
    EventLogs::Create.new(event_object: order, event: event_type).delay(queue: :notifications).call
    check_order_margin if event_type == 'new-custom-order-quoted'
  end

  def check_order_margin
    return if order.commission.blank?

    markdown = 0.0
    yordar_commission = ((1 - (1 - (markdown / 100)) / (1 + (order.commission.to_f / 100))) * 100).round(2)
    return if yordar_commission >= Order::CUSTOM_ORDER_COMMISSION_THRESHOLD

    EventLogs::Create.new(event_object: order, event: 'order-below-margin-threshold', severity: 'warning', commission: yordar_commission).delay(queue: :notifications).call
  end

  def detach_woolworths_account
    woolworths_order = order.woolworths_order
    status = woolworths_order.status
    status += ' Saved for future'
    woolworths_order.update(
      status: status,
      account_in_use: false # disconnect from account
    )
  end

  def order_status
    case
    when is_woolworths_order?
      'saved'
    when quote_params[:mode] === 'save-for-later' && order.is_event_order?
      'draft'
    else
      'quoted'
    end
  end

  def is_woolworths_order?
    @_is_woolworths_order ||= order.woolworths_order.present?
  end

  class Result
    attr_accessor :order, :errors

    def initialize
      @order = nil
      @errors = []
    end

    def success?
      errors.blank? && order.present? && %w[draft saved quoted].include?(order.status)
    end
  end
end

