@keyframes skeltonAnimation {
  0% {
    left:  -30%;
  }
  100% {
    left:  130%;
  }
}

.menu-skeleton {
  background: #e2e2e2 !important;
  position: relative;
  overflow: hidden;
  color: white;
}
.menu-skeleton::before {
  content: '';
  position: absolute;
  background: linear-gradient(to right, #e2e2e2 25%, #d5d5d5 50%, #e2e2e2 100%);
  filter: blur(5px);
  animation: 1s linear 0s infinite skeltonAnimation;
  height: 100%;
  width: 80px;
  top: 0;
}

.menu-skeleton-div {
  padding: 0.7rem 1rem;
}

.drag-handle {
  float: left;
}

.expandable {
  transition: 0.3s ease-in-out width;
}
