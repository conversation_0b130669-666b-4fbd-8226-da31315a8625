class MealPlan < ApplicationRecord

  VALID_KINDS = %w[shared individual].freeze
  VALID_REMINDER_FREQUENCIES = %w[weekly monthly].freeze

  validates :customer_profile, presence: true
  validates :kind, presence: true, inclusion: { in: VALID_KINDS }
  validates :name, presence: true
  validates :delivery_time, presence: true
  validates :delivery_address, presence: true
  validates :delivery_suburb, presence: true
  validates :credit_card, presence: true
  validates :uuid, presence: true
  validates :reminder_frequency, inclusion: { in: VALID_REMINDER_FREQUENCIES }, allow_blank: true

  belongs_to :customer_profile
  belongs_to :delivery_suburb, class_name: 'Suburb', foreign_key: :delivery_suburb_id
  belongs_to :customer_purchase_order, foreign_key: :cpo_id
  belongs_to :gst_free_customer_purchase_order, class_name: 'CustomerPurchaseOrder', foreign_key: :gst_free_cpo_id
  belongs_to :credit_card

  has_many :orders
  has_many :documents, as: :documentable

  delegate :po_number, to: :customer_purchase_order, allow_nil: true
  delegate :po_number, to: :gst_free_customer_purchase_order, allow_nil: true, prefix: 'gst_free'

  # instance methods
  def archived?
    archived_at.present?
  end

end
