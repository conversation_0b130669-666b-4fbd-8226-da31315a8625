# == Schema Information
#
# Table name: users
#
#  id                        :integer          not null, primary key
#  firstname                 :string(255)      not null
#  lastname                  :string(255)
#  gender                    :string(255)      default("Other")
#  admin                     :boolean          default(FALSE)
#  super_admin               :boolean          default(FALSE)
#  email                     :string(255)      default(""), not null
#  encrypted_password        :string(255)      default(""), not null
#  reset_password_token      :string(255)
#  reset_password_sent_at    :datetime
#  remember_created_at       :datetime
#  sign_in_count             :integer          default(0)
#  current_sign_in_at        :datetime
#  last_sign_in_at           :datetime
#  current_sign_in_ip        :string(255)
#  last_sign_in_ip           :string(255)
#  confirmation_token        :string(255)
#  confirmed_at              :datetime
#  confirmation_sent_at      :datetime
#  unconfirmed_email         :string(255)
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  mailchimp_email           :string(255)
#  mailchimp_subscribed      :boolean          default(FALSE)
#  xero_push_fault           :boolean          default(FALSE)
#  business                  :string(255)      default("yr")
#  is_active                 :boolean          default(TRUE)
#  suburb_id                 :integer
#  secondary_email           :string
#  can_access_customers      :boolean
#  can_access_suppliers      :boolean
#  can_access_orders         :boolean
#  allow_all_customer_access :boolean
#  allow_all_supplier_access :boolean
#  hs_id                     :integer
#  can_manage_custom_orders  :boolean          default(FALSE)
#

class User < ApplicationRecord

	devise :database_authenticatable, :recoverable, :rememberable, :trackable, :validatable, :registerable, :confirmable

	VALID_GENDERS = %w[Female Male Other].freeze
	VALID_BUSINESS_TYPES = %w[yr cs].freeze

	validates :gender, inclusion: { in: VALID_GENDERS }, allow_nil: true
	validates :firstname, presence: true
 	validates :business, inclusion: { in: VALID_BUSINESS_TYPES } # Whether this user belongs to Yordar or Category Solutions.

	has_and_belongs_to_many :customer_profiles
	has_and_belongs_to_many :supplier_profiles

	has_many :favourite_customers, as: :favouriter
	has_many :favourite_customer_profiles, through: :favourite_customers, source: :customer_profile

	has_many :leads
	belongs_to :suburb
	has_one :profile, foreign_key: :user_id, dependent: :destroy # user has a profile, but it isn't editable from this model
	has_one :customer_profile, through: :profile, source: :profileable, source_type: 'CustomerProfile'
	has_one :supplier_profile, through: :profile, source: :profileable, source_type: 'SupplierProfile'
	has_many :event_log_views, class_name: 'EventLog::View'
	has_many :assigned_event_logs, class_name: 'EventLog', foreign_key: :assigned_user_id

	after_save :sync_user_with_lead
	after_save :sync_contact_to_hubspot
	after_save :copy_name_to_customer

	# holds profile to be associated at 'create' stage
	attr_accessor :type, :company_name, :just_registered

	# Determine if the user can authenticate or not
	def active?
		super && enabled?
	end

	def name
		"#{firstname} #{lastname}"
	end

	def country_of_origin
		suburb&.country_code || 'AU'
	end

	# Devise : callback
	# A callback method used to deliver confirmation
	# instructions on creation. This can be overriden
	# in models to map to a nice sign up e-mail.
	#
	def send_on_create_confirmation_instructions
	  # send_confirmation_instructions
	  # we are not allowing devise to send any email
	end

	def active_for_authentication?
		super && is_active?
	end

	def email_recipient
		recipient = "#{firstname} #{lastname} <#{email}>"
		recipient += ", #{secondary_email}" if secondary_email.present?
		recipient.gsub(/\r|\n/, '')
	end

	# allow user to log in using any password in development environment
 def valid_password?(password)
   if Rails.env.development?
   	return true
   end

   super
 end

	#
	# Devise email function being overriden with our implementation
	def send_devise_notification(notification, *args)
		case notification
		when :reset_password_instructions
			token = args.first
			Users::Emails::SendResetPasswordEmail.new(user: self, token: token).call
		when :confirmation_instructions
			Users::Emails::SendAccountConfirmationEmail.new(user: self).call
		else
			devise_mailer.send(notification, self, *args).deliver
		end
	end

private

	def sync_user_with_lead
		Lead.where(email: email, user_id: nil).update_all(user_id: id)
	end

	def sync_contact_to_hubspot
		return if just_registered.present?

		# only call syncer once
		hubspot_handler = "%object:Hubspot::SyncContact%value_before_type_cast: #{id}%method_name: :call%"
		if (Rails.env.production? || Rails.env.test?) && Delayed::Job.where('handler ilike ?', hubspot_handler).where(locked_at: nil, failed_at: nil).blank?
			Hubspot::SyncContact.new(contact: self).delay.call
		end
	end

	def copy_name_to_customer
		profile = self.reload.profile # find profile (associated obj may not be available)
		if profile.present? && profile.profileable_type == 'CustomerProfile'
			customer = CustomerProfile.where(id: profile.profileable_id).first
			if customer.present? # update the fields in CustomerProfile model
				customer.update(customer_name: name)
				customer.update(company_name: company_name) if company_name.present?
			end
		end
	end

end
