supplier = @supplier_menu.supplier

supplier_minimums = Suppliers::GetMinimums.new(suppliers: [supplier], categories: params[:category], category_group: params[:category_group]).call
json.partial! 'suppliers/supplier', supplier: supplier, supplier_category: nil, supplier_minimums: supplier_minimums[supplier], has_custom_pricing: @supplier_menu.grouped_rate_cards.present?

if supplier.woolworths?
  grouped_menu_sections = @supplier_menu.section_grouped_menu_items.group_by{|menu_section, _| menu_section.group_name }
  json.section_grouped_menu_items grouped_menu_sections.each do |group_name, section_grouped_menu_items|
    json.name group_name
    json.menu_sections section_grouped_menu_items.each do |menu_section, section_menu_items|
      json.partial! 'menu_sections/menu_section', menu_section: menu_section, menu_items: section_menu_items, favourite_menu_items: @supplier_menu.favourite_menu_items, grouped_rate_cards: @supplier_menu.grouped_rate_cards
    end
  end
  json.delivery_instructions session_profile.present? && session_profile.orders.joins(:woolworths_order).where(status: %w[confirmed delivered new]).order('orders.created_at desc').first.try(:delivery_instruction)
else
  json.section_grouped_menu_items @supplier_menu.section_grouped_menu_items.each do |menu_section, section_menu_items|
    json.partial! 'menu_sections/menu_section', menu_section: menu_section, menu_items: section_menu_items, favourite_menu_items: @supplier_menu.favourite_menu_items, grouped_rate_cards: @supplier_menu.grouped_rate_cards
  end
end

if (recent_orders = @supplier_menu.recent_orders.presence)
  json.partial! 'suppliers/recent_orders', orders: recent_orders
end
