:ruby
  external_order_urls = {
    catering: next_app_supplier_search_url(category_group: 'office-catering', state: '_state', suburb: '_suburb'),
    quote: next_app_customer_quotes_url,
  }

  billing_details_path = @needs_billing_details ? customer_account_and_billing_path(anchor: 'customer-billing-details') : nil
  cart_not_meal_plan = session_order && !session_order.meal_plan_id

- content_for :header_title, 'Shared Meals'

%div{ data: { view_customer_meal_plans: { billingDetailsUrl: billing_details_path, hasMealPlans: @has_meal_plans, cartNotMealPlan: cart_not_meal_plan, mealPlanID: @meal_plan&.id, mealDate: params[:date], locality: 'AU', stripeKey: yordar_credentials(:stripe, :publishable_key), externalOrderUrls: external_order_urls, isAdmin: is_admin? }.to_json, view_order_slideout: true } }

#modal-order-show.reveal.modal.modal-drawer{ data: { reveal: '' } }
