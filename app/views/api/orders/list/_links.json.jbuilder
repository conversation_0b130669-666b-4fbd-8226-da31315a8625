order_links = []
if (meal_plan = order.meal_plan.presence)
  order_links << { type: 'view-package', url: customer_meal_plans_path(mealUUID: meal_plan.uuid, date: order.delivery_at&.to_s(:date_spreadsheet)), label: 'Show Meal Plan' }
end
order_links << { type: 'view', url: order_show_path(order), label: 'View Order' }
if can_edit?(order: order)
  order_links << { type: 'edit', url: next_app_order_edit_url(order), label: 'Edit Order' }
  order_links << { type: 'approve', url: next_app_order_edit_url(order, finaliseQuote: true), label: 'Approve Order' } if order.status == 'quoted'
end

# all normal order can be cloned, but only Yordar admins can clone a custom order
order_links << { type: 'clone', url: api_order_clone_path(order), label: 'Reorder' } if !order.is_event_order? || is_yordar_admin?

cancel_links = []

if %w[voided cancelled delivered].exclude?(order.status)
  cancel_or_skip = order.is_recurrent? ? 'Skip this order as a one-off' : 'Cancel Order'
  cancel_links << { type: 'cancel-one-off', url: api_order_path(order, cancel_mode: 'one-off', format: :json), label: cancel_or_skip }
  order_links << { type: 'export-pdf', url: nil, label: 'Export PDF' }

  if order.is_recurrent?
    cancel_links << { type: 'cancel-subsequent', url: api_order_path(order, cancel_mode: 'subsequent', format: :json), label: "Cancel All #{order.delivery_at.to_s(:weekday)} Orders" }
    cancel_links << { type: 'cancel-related', url: api_order_path(order, cancel_mode: 'related', format: :json), label: 'Cancel All Related Orders' }
    if is_admin?
      if order.status != 'paused'
        cancel_links << { type: 'cancel-on-hold', url: api_order_path(order, cancel_mode: 'on-hold', format: :json), label: "Place All #{order.delivery_at.to_s(:weekday)} Orders On Hold" }
      else
        order_links << { type: 'reactivate-one-off', url: api_order_reactivate_path(order, reactivate_mode: 'one-off', format: :json), label: 'Reactivate This Order as a one-off' }
        order_links << { type: 'reactivate-subsequent', url: api_order_reactivate_path(order, reactivate_mode: 'subsequent', format: :json), label: "Reactivate All On-Hold #{order.delivery_at.to_s(:weekday)} Orders" }
      end
    end
  end
end

if is_admin? && order.invoice_id.blank? && %w[cancelled delivered].include?(order.status)
  cancel_links << { type: 'cancel-void', url: api_order_path(order, cancel_mode: 'void', format: :json), label: 'Void Order' }
end

if cancel_links.size == 1
  order_links += cancel_links
  cancel_links = []
elsif cancel_links.present?
  order_links << { type: 'cancel-options', url: nil, label: 'Cancel Order' }
end

json.links order_links
json.cancel_links cancel_links
