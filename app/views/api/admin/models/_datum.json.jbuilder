json.extract! datum, :id 
@admin_config.class::ADMINABLE_FIELDS.each do |field|
  json.set! field, datum.send(field)
  if %w[supplier_profile_id customer_profile_id overridable_id].include?(field)
    field_name = field.gsub('_id', '')
    json.set! field_name, datum.send(field_name)&.name
  end
  if %w[created_at updated_at].include?(field)
    field_name = field.gsub('_at', '')
    json.set! field_name, datum.send(field).to_s(:full_verbose)
  end
  
end.flatten(1)

