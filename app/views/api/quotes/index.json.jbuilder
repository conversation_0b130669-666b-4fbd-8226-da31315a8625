all_items = []

@quotes.each do |quote|
  all_items << { type: 'quote', item: quote, created_at: quote.created_at }
end

@quoted_orders.each do |order|
  # Skip orders that are already associated with a CustomerQuote to avoid duplicates
  next if order.customer_quote_id.present? && @quotes.any? { |q| q.id == order.customer_quote_id }

  all_items << { type: 'order', item: order, created_at: order.created_at }
end

all_items.sort_by! { |item| item[:created_at] }.reverse!

json.array! all_items.each do |item_data|
  if item_data[:type] == 'quote'
    json.partial! 'api/quotes/quote', quote: item_data[:item]
  else
    # <PERSON><PERSON> quoted order using the same format as the orders list
    order = item_data[:item]
    json.partial! 'api/orders/list/order', order: order
  end
end