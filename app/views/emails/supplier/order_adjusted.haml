%p
  Hi #{firstname}
%p
  The order
  - if order.has_no_orderlines
    %strong
      = "##{order.id}"
  - else
    %a{ href: order.pdf_url, target: '_blank', style: email_link_style }
      = "##{order.id}"
  for
  %em
    = "`#{order.customer_name}`"
  has been
  = succeed '.' do
    %strong<
      adjusted

%p Note this change is to reflect any adjustments that were made to the order, post delivery.

- if order.has_no_orderlines

  %p
    All items have been removed from this order. Please
    %strong
      CANCEL 
    this delivery.

  %p
    Your can contact
    %a{ href: orders_email, style: email_link_style}<
      = orders_email
    for more information.

- else

  - if item_changes.present?
    %table{ width: '100%' }
      %tr
        %td{ style: 'border-bottom: 2px dotted #ededed;', colspan: 2 }
          %strong{ style: 'font-size: 16px;' }
            Item Changes

      - item_changes.each_with_index do |location, idx|
        - location_changes = location.changes
        - location_is_removed = location.change_type == 'removed'
        - is_multi_column = location_changes.present? || location_is_removed
        - if idx >0
          %tr
            %td{ colspan: 2 }
              &nbsp;
        %tr
          %td{ style: "font-size: 15px;border-bottom: 2px solid #ededed;#{is_multi_column ? 'width: 50%;' : ''}", colspan: is_multi_column ? 1 : 2 }
            %strong
              = location.details
              - if location.change_type == 'created'
                %em (new)
          - if location_is_removed
            %td{ style: 'border-bottom: 2px solid #ededed;' }
              %strong{ style: 'color: #d9534f;' }
                removed
          - if location_changes.present?
            %td{ style: 'border-bottom: 2px solid #ededed;' }
              %strong
                = location.change_type
              - if location_changes.present?
                - location.changes.each_with_index do |change, lidx|
                  - if lidx > 0
                    %br/
                  from 
                  %em
                    = change.old_value
                  to
                  %em
                    = change.new_value
        - location.order_line_changes.each do |order_line|
          - order_line_changes = order_line.changes.presence
          - order_line_is_removed = order_line.change_type == 'removed'
          - is_ol_multi_column = order_line_changes.present? || order_line_is_removed
          %tr
            %td{ style: "border-bottom: 1px solid #ededed;#{is_ol_multi_column ? 'width: 50%;' : ''}", colspan: is_ol_multi_column ? 1 : 2 }
              = order_line.name
            - if order_line_is_removed
              %td{ style: 'border-bottom: 1px solid #ededed;' }
                %strong{ style: 'color: #d9534f;' }
                  removed
            - if order_line_changes.present?
              %td{ style: 'border-bottom: 1px solid #ededed;' }
                - order_line_changes.each_with_index do |change, olidx|
                  - if olidx > 0
                    %br/
                  %strong
                    = "#{change.field} - "
                  - if change.field == 'quantity' && change.old_value.present?
                    = change.new_value
                    - if change.new_value > change.old_value
                      %span{ style: 'font-size: 18px; color: #1f9e86' }
                        &uarr;
                    - else
                      %span{ style: 'font-size: 18px; color: #d9534f;' }
                        &darr;                    
                  - elsif order_line.change_type == 'updated' && change.old_value.present?
                    = "from #{change.old_value} to #{change.new_value}"
                  - else # order_line.change_type == 'created'
                    = change.new_value
                    %em [new]

  %p
    Please refer to
    %a{ href: order.pdf_url, style: email_link_style }
      Version #{order.pdf_version}
    of this order document for further details and update your records accordingly.

  - if order.flex_url.present?
    %p
      Also find attached an updated
      %strong
        %a{ href: order.flex_url, target: '_blank', style: email_link_style } JSON file
      to be used to update order into your Flex Catering App.
    
  %p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
    = render 'emails/button', text: 'View Order', link: order.view_url, kind: :bordered, is_last: true

  - if has_commission
    %p
      %strong N.B.
      Pricing now displayed on the order form is your supplied pricing; Any Yordar discounted total will be calculated at the bottom. This will make it easier for you to spot pricing discrepancies moving forward.

