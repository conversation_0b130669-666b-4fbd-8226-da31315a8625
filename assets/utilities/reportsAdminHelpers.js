import axios from 'axios';
import { dateToDateString } from 'utilities/graph-options';
import { apiReportsPath } from 'routes';

export const fetchReports = async ({ sourceType, sourceTypes, reportType, dates, options }) => {
  return axios({
    method: 'GET',
    url: apiReportsPath({ format: 'json' }),
    params: {
      ...(sourceType && { source_type: sourceType }),
      ...(sourceTypes && { source_types: sourceTypes }),
      report_type: reportType,
      start_date: dateToDateString(dates.start, '01'),
      end_date: dateToDateString(dates.end, new Date(dates.end.getFullYear(), dates.end.getMonth() + 1, 0).getDate()),
      ...options,
      is_admin_reports: true,
      react_data: true,
    },
  });
};

export const asyncSelectStyles = {
  control: (baseStyles) => ({
    ...baseStyles,
    zIndex: 5,
    height: '37px',
    minHeight: '37px',
    borderRadius: '18.5px',
    display: 'flex',
    alignItems: 'center',
    boxShadow: '0 2px 5px 1px #403c4329',
    border: 'none',
  }),
  valueContainer: (baseStyles) => ({
    ...baseStyles,
    height: '35px',
    padding: '0 16px',
    display: 'flex',
    alignItems: 'center',
  }),
  input: (baseStyles) => ({
    ...baseStyles,
    margin: '0',
    padding: '0',
    paddingLeft: '22px',
  }),
  placeholder: (baseStyles) => ({
    ...baseStyles,
    position: 'absolute',
    left: '16px',
  }),
  indicatorsContainer: (baseStyles) => ({
    ...baseStyles,
    height: '35px',
    paddingRight: '8px',
  }),
  indicatorSeparator: () => ({
    display: 'none',
  }),
  dropdownIndicator: (baseStyles) => ({
    ...baseStyles,
    padding: '4px',
  }),
  clearIndicator: (baseStyles) => ({
    ...baseStyles,
    padding: '4px',
  }),
}