const Spinner = ({ isSmall, bgColor = false, wheel }) => {
  if (wheel) {
    return (
      <div className="lds-ring-container">
        <div className="lds-ring">
          <div />
          <div />
          <div />
          <div />
        </div>
      </div>
    );
  }
  return (
    <span className={`sk-three-bounce ${isSmall ? 'small-bounce' : ''} ${bgColor || ''}`}>
      <span className="sk-child sk-bounce1" />
      <span className="sk-child sk-bounce2" />
      <span className="sk-child sk-bounce3" />
    </span>
  );
};

export default Spinner;
