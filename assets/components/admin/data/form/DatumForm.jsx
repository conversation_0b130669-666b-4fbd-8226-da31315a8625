import { useState } from 'react';

// store
import shallow from 'zustand/shallow';
import dataAdminStore from 'store/admin/dataAdminStore';

// components
import DatumField from './DatumField';
import OauthApplicationOwner from './OauthApplicationOwner';

const DatumForm = ({ datum, model, fields, showForm }) => {
  const [localDatum, setLocalDatum] = useState(datum);

  const { createDatum, updateDatum } = dataAdminStore(
    (state) => ({
      createDatum: state.createDatum,
      updateDatum: state.updateDatum,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localDatum.id) {
        await updateDatum({
          datum: localDatum,
          model,
        });
      } else {
        await createDatum({
          datum: localDatum,
          model,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalDatum((state) => ({ ...state, [field]: value }));
  };

  const editFields = fields.filter((field) => datum.id ? field.in_edit : field.in_new);

  return (
    <>
      <div className="overlay show" onClick={() => showForm(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list">
            <h3>
              {!!datum.id ? `Edit ${model} - ${datum.name}` : `New ${model}`}
            </h3>
            {editFields.map((field) => (
              <div>
                <DatumField field={field} model={model} localDatum={localDatum} handleChange={handleChange} />
              </div>
            ))}

            {model === 'OauthApplication' && !!datum.id && <OauthApplicationOwner localDatum={localDatum} setLocalDatum={setLocalDatum} />}
          </div>
          <div className="between-flex mt-1">
            {!!editFields.length && (
              <a className="button" onClick={handleSave}>
                Save
              </a>  
            )}
            <a className="button gray-btn" onClick={() => showForm(false)}>
              Cancel
            </a>
          </div>
        </div>        
      </div>
    </>
  );
};

export default DatumForm;
