import FieldInfo from './FieldInfo';

const DatumDetails = ({ datum, model, fields, showDetails }) => {
  const showFields = fields.filter((field) => field.in_show);

  return (
    <>
      <div className="overlay show" onClick={() => showDetails(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list">
            <h3>
              {model} - {datum.name || datum.id}
            </h3>
            
            {showFields.map((field) => (
              <div className='mb-2'>
                <label><strong>{field.label}</strong></label>
                <FieldInfo datum={datum} field={field} />
              </div>
            ))}

          </div>
          <div className="between-flex mt-1">
            <a className="button gray-btn" onClick={() => showDetails(false)}>
              Close
            </a>
          </div>
        </div>        
      </div>
    </>
  );
};

export default DatumDetails;
