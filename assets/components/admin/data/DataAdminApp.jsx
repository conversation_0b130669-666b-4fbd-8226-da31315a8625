import { useEffect, useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import dataAdminStore from 'store/admin/dataAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewDatum from './NewDatum';
import Datum from './Datum';
import DataSearch from './DataSearch';
import DataSkeleton from './DataSkeleton';
import NoDataNotice from './NoDataNotice';

const DataAdminApp = () => {
  const { model } = useContext(appContext);

  const { fetchConfig, fields, fetchData, data, loadingList, loadingMore, page, hasMore, query } = dataAdminStore(
    (state) => ({
      fetchConfig: state.fetchConfig,
      fields: state.fields,
      fetchData: state.fetchData,
      data: state.data,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  useEffect(async () => {
    fetchConfig({
      model
    });
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchData({
          page,
          model,
        });
      }
    },
  });

  const listFields = fields.filter((field) => field.in_list);
  const newFields = fields.filter((field) => field.in_new);
  const showFields = fields.filter((field) => field.in_show);
  const editFields = fields.filter((field) => field.in_edit);

  return (
    <>
      <DataSearch />
      {!loadingList && (
        <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
          <div className="item-list__headings sticky">
          <span className="list-flex-1" />
            {listFields.map((field) => (
              <span className="list-flex-2">{field.label}</span>
            ))}
            {!!(showFields.length || editFields.length) && <span className="list-flex-1 text-center">Action</span>}
          </div>
        </div>
      )}
      {!!newFields.length && !loadingList && !loadingMore && !query && <NewDatum model={model} fields={fields} />}
      <NoDataNotice isLoading={loadingList || loadingMore} data={data} />
      {data.map((datum, idx) => (
        <Datum key={`datum-${datum.id}`} datum={datum} index={idx} model={model} fields={fields} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <DataSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default DataAdminApp;
