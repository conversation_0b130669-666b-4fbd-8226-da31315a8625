const FieldInfo = ({ datum, field }) => {
  if (['select', 'string', 'integer', 'decimal'].includes(field.type)) {
    return datum[field.name.replace('_id','')] || datum[field.name];
  }

  if (field.type === 'datetime') {
    return datum[field.name.replace('_at','')] || datum[field.name]
  }

  if (field.type === 'boolean') {
    return <div className={`admin-flags ${!!datum[field.name] ? 'active' : 'inactive'}`} />;
  }

  if (field.type === 'array') {
    return (
      <>
        {datum[field.name].map((value) => (
          <>
            <span>{value}</span>
            <br />
          </>
        ))}
      </>
    )
  }

  if (field.type === 'jsonb') {
    return (
      <>
        {Object.keys(datum[field.name]).map((key) => (
          <>
            <strong>{key}: </strong>
            <span>{datum[field.name][key]}</span>
            <br />
          </>
        ))}
      </>
    )
  }

  return datum[field.name];
}

export default FieldInfo;