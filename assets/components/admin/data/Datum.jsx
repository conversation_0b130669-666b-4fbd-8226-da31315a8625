import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import FieldInfo from './FieldInfo';
import DatumForm from './form/DatumForm';
import DatumDetails from './DatumDetails';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const Datum = ({ datum, index, model, fields }) => {
  const [isEdit, setIsEdit] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const circleColor = getCircleIconColor(index);

  const listFields = fields.filter((field) => field.in_list);
  const showFields = fields.filter((field) => field.in_show);
  const editFields = fields.filter((field) => field.in_edit);
  const iconLabel = datum.name || datum.kind || datum.customer_profile || datum.supplier_profile;

  return (
    <>
      <div className="list-item">
        <div className="list-flex-1 invoice-header">
          <span className="circle-icon" style={{ background: circleColor }}>
            {iconLabel[0]?.toUpperCase()}
          </span>
        </div>
        {listFields.map((field) => <div className="list-flex-2"><FieldInfo datum={datum} field={field} /></div>)}
        {!!(showFields.length || editFields.length) && (
          <div className="list-flex-1 text-center">
            {!!showFields.length && <a className="order-list-item__options-button" onClick={() => setIsShow(!isShow)} />}
            {!!editFields.length && <a className="icon-edit" onClick={() => setIsEdit(!isEdit)} />}
          </div>
        )}
      </div>
      {!!showFields.length && isShow && <DatumDetails datum={datum} model={model} fields={fields} showDetails={setIsShow} />}
      {!!editFields.length && isEdit && <DatumForm datum={datum} model={model} fields={fields} showForm={setIsEdit} />}
    </>
  )
};

Datum.propTypes = {
  datum: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  model: PropTypes.string.isRequired,
};

export default Datum;
