import { useEffect, useState } from 'react';
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import DatePicker from 'react-datepicker';
import { getMonthsAgoDate, getDateRangeTitle } from 'utilities/graph-options';
import { fetchReports, asyncSelectStyles } from 'utilities/reportsAdminHelpers';
import { apiAdminSuppliersPath } from 'routes';

import ReportsGraph from './ReportsGraph';
import NoResults from './NoResults';
import Spinner from 'components/utilities/Spinner';
import SupplierReportExportModal from './export/SupplierReportExportModal';

const SupplierAdminReports = () => {
  const sourceType = 'SupplierProfile';

  const [reportData, setReportData] = useState([]);
  const [reportType, setReportType] = useState('monthly');
  const [excludeStaffing, setExcludeStaffing] = useState(true);
  const [dates, setDates] = useState({ start: getMonthsAgoDate(1), end: getMonthsAgoDate(0) });
  const [loading, setLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [activeSupplier, setActiveSupplier] = useState();

  function onDateChange(newDates) {
    const [start, end] = newDates;
    setDates({ start, end });
  }

  useEffect(async () => {
    if (!dates.start || !dates.end) return;

    setLoading(true);
    const { data } = await fetchReports({
      sourceTypes: ['CustomerProfile', 'SupplierProfile'],
      reportType,
      dates,
      options: {
        ...(!!activeSupplier && { supplier_id: activeSupplier.id }),
        ...(!!excludeStaffing && { exclude_staffing: true }),
      }
    });
    setLoading(false);
    setReportData(data.report_data);
  }, [dates, activeSupplier, reportType, excludeStaffing]);

  const promiseOptionsSuppliers = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseSuppliers } = await axios({
      method: 'GET',
      url: apiAdminSuppliersPath(),
      params: { query },
    });

    return responseSuppliers.map((supplier) => ({
      value: supplier.id,
      label: supplier.name,
      id: supplier.id,
      email: supplier.email,
    }));
  }, 1000);

  return (
    <>
      <div className="dashboard-filters reports-filters between-flex" style={{ marginRight: 0 }}>
        <span>Sales Report</span>
        {loading && <Spinner bgColor="black" />}
        <div className="between-flex">
          <div className="dashboard-filter dashboard-filter__datepicker">
            <span className="between-flex">For Dates: </span>
            <div className="between-flex">
              <DatePicker
                startDate={dates.start}
                endDate={dates.end}
                selected={dates.start}
                onChange={(newDates) => onDateChange(newDates)}
                dateFormat="MM/yyyy"
                showMonthYearPicker
                selectsRange
                className="dashboard-filter"
              />
            </div>
          </div>          
          
          <AsyncSelect
            className="form-input reports-search"
            cacheOptions
            defaultOptions
            isClearable
            placeholder="Search Suppliers"
            loadOptions={promiseOptionsSuppliers}
            onChange={(selected) => setActiveSupplier(selected)}
            value={activeSupplier}
            styles={asyncSelectStyles}
          />
        </div>
      </div>
      <div className='between-flex'>
        <div style={{ display: 'flex' }}>
          <div className="report-period-options">
            <label style={{ marginRight: '1rem' }}>
              <input
                type="radio"
                name="reportType"
                value="monthly"
                checked={reportType === 'monthly'}
                onChange={() => setReportType('monthly')}
              />
              Monthly
            </label>
            <label>
              <input
                type="radio"
                name="reportType"
                value="weekly"
                checked={reportType === 'weekly'}
                onChange={() => setReportType('weekly')}
              />
              Weekly
            </label>
          </div>
          <label style={{ marginLeft: '1rem' }}>
            <input type="checkbox" checked={excludeStaffing} onChange={() => setExcludeStaffing(!excludeStaffing)} />
            Exclude Staffing Spends
          </label>
        </div>
        {activeSupplier && <a className='button tiny' onClick={() => setIsExporting(true)}>Export</a>}
      </div>
      <ReportsGraph
        reportData={reportData}
        type={sourceType}
        loading={loading}
        title={activeSupplier ? `${activeSupplier.label} - Supplier Report` : undefined}
        chartTitle={getDateRangeTitle(dates)}
      />
      <div>
        {!reportData.length && !loading && (
          <NoResults sourceType={sourceType} />
        )}
      </div>
      {!!activeSupplier && isExporting && <SupplierReportExportModal dates={dates} activeSupplier={activeSupplier} setIsExporting={setIsExporting} />}
    </>
  );
};

export default SupplierAdminReports;
