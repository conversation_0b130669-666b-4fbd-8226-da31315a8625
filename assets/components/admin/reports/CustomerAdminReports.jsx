import { useEffect, useState } from 'react';
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import DatePicker from 'react-datepicker';
import { getMonthsAgoDate, getDateRangeTitle } from 'utilities/graph-options';
import { fetchReports, asyncSelectStyles } from 'utilities/reportsAdminHelpers';
import { apiAdminCustomersPath } from 'routes';

import ReportsGraph from './ReportsGraph';
import ReportDoughnut from './ReportDoughnut';
import NoResults from './NoResults';
import Spinner from 'components/utilities/Spinner';

const CustomerAdminReports = ({ isAdmin }) => {
  const sourceType = 'CustomerProfile';
  const reportType = 'monthly';

  const [report, setReport] = useState({});
  const [reportData, setReportData] = useState([]);  
  const [dates, setDates] = useState({ start: getMonthsAgoDate(isAdmin ? 1 : 5), end: getMonthsAgoDate(0) });
  const [loading, setLoading] = useState(true);

  const [activeCustomer, setActiveCustomer] = useState();

  function onDateChange(newDates) {
    const [start, end] = newDates;
    setDates({ start, end });
  }

  useEffect(async () => {
    if (!dates.start || !dates.end) return;

    setLoading(true);
    const { data } = await fetchReports({
      sourceType,
      reportType,
      dates,
      options: {
        ...(!!activeCustomer && { customer_id: activeCustomer.id }),
      }
    });
    setLoading(false);
    setReport(data);
    setReportData(data.report_data);
  }, [dates, activeCustomer]);

  const promiseOptionsCustomers = debounce(async (query) => {
    // For company admins (non-admin users), show all accessible users by default
    // For admin users, require at least 3 characters to search
    if (isAdmin && (!query || query.length < 3)) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: {
        ...(query && { query }),
        // For company admins, get all customers they have access to
        ...(!isAdmin && { limit: 50 }), // Reasonable limit for dropdown
      },
    });

    return responseCustomers.map((customer) => ({
      value: customer.id,
      label: customer.name,
      id: customer.id,
      email: customer.email,
    }));
  }, 1000);

  return (
    <>
      <div className="dashboard-filters reports-filters between-flex" style={{ marginRight: 0 }}>
        <span>Spends Report</span>
        {loading && <Spinner bgColor="black" />}
        <div className="between-flex">
          <div className="dashboard-filter dashboard-filter__datepicker">
            <span className="between-flex">For Dates: </span>
            <div className="between-flex">
              <DatePicker
                startDate={dates.start}
                endDate={dates.end}
                selected={dates.start}
                onChange={(newDates) => onDateChange(newDates)}
                dateFormat="MM/yyyy"
                showMonthYearPicker
                selectsRange
                className="dashboard-filter"
              />
            </div>
          </div>          
          <AsyncSelect
            className="form-input reports-search no-border"
            cacheOptions
            defaultOptions={!isAdmin} // Load default options for company admins
            isClearable
            placeholder={isAdmin ? '🔍  Search Customers' : '🔍  Select Team Member'}
            loadOptions={promiseOptionsCustomers}
            onChange={(selected) => setActiveCustomer(selected)}
            value={activeCustomer}
            styles={asyncSelectStyles}
          />
        </div>
      </div>
      {!!reportData.length && (
        <div className="reporting">
          <ReportsGraph
            reportData={reportData}
            type={sourceType}
            loading={loading}
            title={activeCustomer ? `${activeCustomer.label} - Spending Report` : undefined}
            chartTitle={getDateRangeTitle(dates)}
          />
          <ReportDoughnut report={report} title="category" fields={['catering', 'snacks']} />
          <ReportDoughnut report={report} title="supplier" fields={['ethical', 'suppliers']} />
        </div>
      )}
      <div>
        {!reportData.length && !loading && (
          <NoResults sourceType={sourceType} />
        )}
      </div>
    </>
  );
};

export default CustomerAdminReports;
