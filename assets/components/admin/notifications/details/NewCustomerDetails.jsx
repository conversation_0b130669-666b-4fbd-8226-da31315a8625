import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import Spinner from 'components/utilities/Spinner';
import { apiAdminNotificationPath } from 'routes';

const NewCustomerDetails = ({ notification, setNotificationInfo }) => {
  const [customer, setCustomer] = useState(null);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data: responseCustomer } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      setCustomer(responseCustomer);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">New Customer</h3>
      </div>
      {loadingInfo && <Spinner wheel />}
      {!loadingInfo && (
        <div className="order-show-details-section">
          <p>
            <strong>Name: </strong>
            <span>{customer.customer_name}</span>
          </p>
          <p>
            <strong>Company: </strong>
            <span>{customer.company_name}</span>
          </p>
          {!!customer.suburb && (
            <p>
              <strong>Suburb: </strong>
              <span>{customer.suburb}</span>
            </p>
          )}
          <p>
            <strong>Email: </strong>
            <a href={`mailto:${customer.email}`}>{customer.email}</a>
          </p>
          {!!customer.phones?.length && (
            <p>
              <strong>Phone: </strong>
              {customer.phones.map((phone) => <a className="mr-1-4" href={`tel:${phone}`}>{phone}</a>)}
            </p>
          )}
          <p>
            <strong>Role: </strong>
            <span>{customer.role}</span>
          </p>
        </div>
      )}

      <div className="order-show-details-section">
        {!!customer?.sign_in_path && (
          <a className="button m-0 hollow" style={{ width: '100%' }} href={customer.sign_in_path} target="blank">
            Access Profile
          </a>
        )}
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default NewCustomerDetails;
