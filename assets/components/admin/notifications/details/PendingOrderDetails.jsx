import { useState, useEffect } from 'react';

// action
import axios from 'axios';

// components
import Spinner from 'components/utilities/Spinner';
import { apiAdminNotificationPath } from 'routes';
import SimpleOrderDetails from './SimpleOrderDetails';

const PendingOrderDetails = ({ notification, setNotificationInfo }) => {
  const [customOrders, setCustomOrders] = useState([]);
  const [normalOrders, setNormalOrders] = useState([]);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      const { custom_orders: customOrders, normal_orders: normalOrders } = data;
      setCustomOrders(customOrders || []);
      setNormalOrders(normalOrders || []);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">Pending Orders (x{notification.pending_orders_count})</h3>
      </div>
      <p>
        To be delivered: <strong>{notification?.delivery_on}</strong>
      </p>
      {loadingInfo && <Spinner wheel />}
      {!loadingInfo && !!customOrders.length && (
        <div className="order-show-details-section">
          <p className="text-center">
            <strong>Custom Orders (x{customOrders.length})</strong>
          </p>
          {customOrders.map((order) => (
            <SimpleOrderDetails order={order} />
          ))}
        </div>
      )}
      {!loadingInfo && !!normalOrders.length && (
        <div className="order-show-details-section">
          <p className="text-center">
            <strong>Normal Orders (x{normalOrders.length})</strong>
          </p>
          {normalOrders.map((order) => (
            <SimpleOrderDetails order={order} />
          ))}
        </div>
      )}
      <div className="order-show-details-section">
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default PendingOrderDetails;
