import { useState, useEffect } from 'react';

// action
import axios from 'axios';

// components
import Spinner from 'components/utilities/Spinner';
import { apiAdminNotificationPath } from 'routes';
import OrderInvoiceDetails from './OrderInvoiceDetails';
import OrderSuppliersDetails from './OrderSuppliersDetails';

const OrderDetails = ({ notification, setNotificationInfo }) => {
  const [order, setOrder] = useState(null);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data: responseOrder } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      setOrder(responseOrder);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  if (loadingInfo) {
    return <Spinner wheel />;
  }

  const { totals } = order;

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">Delivery Details</h3>
        <div className="between-flex">
          <a className="button tiny" href={order.order_view_path} target="blank">
            View Full Order
          </a>
          <a className="button tiny ml-1-4" href={order.order_edit_path} target="blank">
            Edit Order
          </a>
        </div>
      </div>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title order">
          {order.order_variant == 'event_order' ? 'Custom Order' : 'Order'}
        </h6>
        <p>
          <strong>Name: </strong>
          <span>{order.name}</span>
        </p>
        <p>
          <strong>Status: </strong>
          {order.status === 'pending' ? (
            <span className={`status-icon with-tooltip ${order.status}`}>
              In Progress <span className="icon-info-circle" />
            </span>
          ) : (
            <span className={`status-icon ${order.status}`}>
              {order?.status?.charAt(0)?.toUpperCase() + order?.status?.slice(1)}
            </span>
          )}
        </p>
        <p>
          <strong>Order: </strong>
          <span>#{order.id}</span>
        </p>
        <p>
          <strong>Delivery: </strong>
          {order?.delivery_at}
        </p>
        <p>
          <strong>Type: </strong>
          {order.is_recurrent ? 'Recurring Order' : 'One-off Order'}
        </p>
        {order.po_number && (
          <p>
            <strong>PO number: </strong>
            {order.po_number}
          </p>
        )}
        {!!order.billing_frequency &&
          ['new', 'amended', 'pending', 'confirmed', 'delivered'].includes(order.status) && (
            <OrderInvoiceDetails order={order} />
          )}
      </div>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title delivery">Delivery To</h6>
        <p>{order.customer_name}</p>
        <p>{order.address}</p>
        {loadingInfo && <div className="order-map order-map--small loading" />}
        {!!order.map_url && (
          <div className="order-map order-map--small">
            <img src={order.map_url} />
          </div>
        )}
      </div>
      {order?.delivery_instruction && (
        <div className="order-show-details-section">
          <h3 className="order-show-detail-title instructions">Instructions</h3>
          <p>{order.delivery_instruction}</p>
        </div>
      )}
      <OrderSuppliersDetails suppliers={order?.suppliers} />
      <div className="order-show-details-section">
        <div className="customer-order__totals">
          <p className="between-flex grey">
            Subtotal
            <span>{totals.subtotal}</span>
          </p>

          <p className="between-flex grey">
            GST
            <span>{totals.gst}</span>
          </p>

          <p className="between-flex grey">
            Total
            <span>{totals.total}</span>
          </p>
          {!!order.yordar_commission && (
            <p className="between-flex grey">
              Commission
              <span className={order.commission_below_recommendation ? 'text-alert' : ''}>
                {order.yordar_commission}
              </span>
            </p>
          )}
        </div>
      </div>
      <div className="order-show-details-section">
        <a className="button m-0 hollow" style={{ width: '100%' }} href={order.customer_sign_in_path} target="blank">
          Access Profile
        </a>
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default OrderDetails;
