import React from 'react';
import Quote from './Quote';
import OrderListItem from '../orders/OrderList/OrderListItem';
import OrderListHeadings from '../orders/OrderList/OrderListHeadings';

const QuotesList = ({ quotes, setActiveQuote, setOpenModal, setModalInfo, isAdmin }) => {
  const quoteRequests = quotes.filter((item) => !item.suppliers);
  const quotedOrders = quotes.filter((item) => item.suppliers);

  return (
    <>
      {quoteRequests.length > 0 && (
        <div className="mb-1">
          <div className="section-heading">
            <h3>Quote Requests</h3>
          </div>
          <div className="customer-invoices__headings">
            <span className="list-flex-2">Created</span>
            <span className="list-flex-1">Occasion</span>
            <span className="list-flex-2 text-center">For Date</span>
            <span className="list-flex-2 text-center">Status</span>
            <span className="list-flex-1 px-1-2">Actions</span>
          </div>
          {quoteRequests.map((quote, idx) => (
            <Quote key={`customer-quote-${quote.id}`} quote={quote} index={idx} setActiveQuote={setActiveQuote} />
          ))}
        </div>
      )}

      {quotedOrders.length > 0 && (
        <>
          <div className="section-heading">
            <h3>Quoted Orders</h3>
          </div>
          <OrderListHeadings />
          {quotedOrders.map((order) => (
            <OrderListItem
              key={`quoted-order-${order.id}`}
              order={order}
              setOpenModal={setOpenModal}
              setModalInfo={setModalInfo}
              isAdmin={isAdmin}
            />
          ))}
        </>
      )}
    </>
  );
};

export default QuotesList;
