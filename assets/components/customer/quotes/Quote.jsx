import { useContext } from 'react';
import PropTypes from 'prop-types';

import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';
import shallow from 'zustand/shallow';
import customerQuotesStore from 'store/customerQuotesStore';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const Quote = ({ quote, index, setActiveQuote }) => {
  const circleColor = getCircleIconColor(index);
  const { externalOrderUrls } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);

  const { removeQuote } = customerQuotesStore(
    (state) => ({
      removeQuote: state.removeQuote,
    }),
    shallow
  );

  let newOrderUrl;
  if (isAdmin && ['submitted', 'quoted'].includes(quote.status)) {
    newOrderUrl = ['catering', 'event'].includes(quote.kind) ? externalOrderUrls.catering : externalOrderUrls.pantry;
    if (newOrderUrl.includes('http')) {
      newOrderUrl += `?quoteUUID=${quote.uuid}`;
      if (quote.order_date) {
        newOrderUrl += `&for_date=${quote.order_date}`;
      }
    }
  }

  return (
    <>
      <div className="customer-invoice customer-data">
        <div className="list-flex-2 invoice-header">
          <span className="circle-icon" style={{ background: circleColor }}>
            {quote.day_month}
          </span>
          <strong className="invoice-date">{quote.created_at}</strong>
        </div>
        <div className="list-flex-1">
          {quote.Occasion ? `${quote.Occasion} (${quote.kind})` : quote.kind.toUpperCase()}
        </div>
        <div className="list-flex-2 text-center">
          {quote.Date && quote.Time ? `${quote.Time} on ${quote.Date}` : '-'}
        </div>
        <div className="list-flex-2 text-center">{quote.status.toUpperCase()}</div>
        <div className="list-flex-1 between-flex px-1-2">
          <a onClick={() => setActiveQuote(quote)}>View</a>
          {isAdmin && ['submitted', 'quoted'].includes(quote.status) && (
            <>
              {newOrderUrl && (
                <a href={newOrderUrl} target="_blank" rel="noreferrer">
                  Create Order
                </a>
              )}
              <a className="icon-trash admin-action" onClick={() => removeQuote({ quote })} />
            </>
          )}
        </div>
      </div>
    </>
  );
};

Quote.propTypes = {
  quote: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  setActiveQuote: PropTypes.func.isRequired,
};

export default Quote;
