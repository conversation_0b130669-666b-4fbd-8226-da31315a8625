import { useContext, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import appContext from 'contexts/appContext';
import moment from 'moment';
import mealPlansStore from 'store/mealPlansStore';

// components
import CalendarOrderCreatePopup from 'components/customer/orders/OrderCalendar/CalendarOrderCreatePopup';
import MealPlanHoliday from './MealPlanHoliday';
import MealPlanOrder from './MealPlanOrder';

const MealPlanDay = ({ day, index, holidays, orders, selectedMealPlan, setModalInfo, setOpenModal, forOrders }) => {
  const { externalOrderUrls, cartNotMealPlan } = useContext(appContext);
  const { setClearCartModalOpen, setSavedOrderUrl } = mealPlansStore((state) => ({
    setClearCartModalOpen: state.setClearCartModalOpen,
    setSavedOrderUrl: state.setSavedOrderUrl,
  }));

  const [showAddNewOrderLinks, setShowAddNewOrderLinks] = useState(false);
  const popupRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setShowAddNewOrderLinks(false);
      }
    };

    if (showAddNewOrderLinks) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAddNewOrderLinks]);

  const dayHolidays = holidays.filter((event) => event.on_date === day);
  const dayOrders = orders.filter((event) => event.delivery_date === day);
  const canOrder = moment().isBefore(day, 'day');

  const forDate = `${moment(day).format('YYYY-MM-DD')}T${selectedMealPlan?.delivery_time_stamp}`;

  const newOrderUrl = `${externalOrderUrls.catering
    .replace('_suburb', selectedMealPlan?.suburb)
    .replace('_state', selectedMealPlan?.state)}?mealUUID=${selectedMealPlan?.uuid}`;

  const addNewOrder = (url) => {
    if (cartNotMealPlan) {
      setSavedOrderUrl(url);
      return setClearCartModalOpen(true);
    }
    window.location = url;
  };

  const dayNumber = day.split('-')[2];
  const isToday = moment().isSame(day, 'day');

  return (
    <div key={index} className={`meal-plan-day ${isToday ? 'today' : ''}`} data-date={day} ref={popupRef}>
      <div className="events">
        <div className={`day-number ${!dayHolidays.length ? 'spacer' : ''} ${isToday ? 'today' : ''}`}>{dayNumber}</div>
        {dayHolidays.map((holiday, index) => (
          <MealPlanHoliday
            key={`meal-plan-day-holiday-${holiday.id}`}
            holiday={holiday}
            index={index}
            date={day}
            selectedMealPlan={selectedMealPlan}
            forOrders={forOrders}
          />
        ))}
        {dayOrders.map((order, index) => (
          <MealPlanOrder
            key={`meal-plan-day-order-${order.id}`}
            order={order}
            index={index}
            setModalInfo={setModalInfo}
            setOpenModal={setOpenModal}
          />
        ))}
        {showAddNewOrderLinks && <CalendarOrderCreatePopup showDate={moment(day).format('Do MMM')} dateParam={day} />}
      </div>
      {canOrder && !forOrders && (
        <a onClick={() => addNewOrder(`${newOrderUrl}&for_date=${forDate}`)} className="add-meal-plan-day">
          Add New
        </a>
      )}
      {canOrder && forOrders && (
        <div className="popup-container">
          <a
            onClick={() => setShowAddNewOrderLinks((state) => !state)}
            className={`add-meal-plan-day${showAddNewOrderLinks ? ' close' : ''}`}
          >
            {showAddNewOrderLinks ? 'Close' : 'Add New'}
          </a>
        </div>
      )}
    </div>
  );
};

MealPlanDay.propTypes = {
  day: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  holidays: PropTypes.array.isRequired,
  orders: PropTypes.array.isRequired,
  selectedMealPlan: PropTypes.object,
};

export default MealPlanDay;
