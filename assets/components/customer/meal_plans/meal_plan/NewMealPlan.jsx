import PropTypes from 'prop-types';
import MealPlanForm from './MealPlanForm';

const initialMealPlan = {
  id: null,
  kind: 'shared',
  name: '',
  delivery_time: '',
  delivery_address_level: '',
  delivery_address: '',
  delivery_suburb_id: null,
  delivery_instruction: '',
  number_of_people: '',
  suburb_label: '',
  credit_card_id: null,
};

const NewMealPlan = ({ setIsNew, billingDetailsUrl }) => (
  <div className="auto-container">
    <div className="auth-card">
      <div className="auth-card__illustration">
        <h4>Create A Shared Meal Plan</h4>
        <img src="/assets/illustrations/team-sharing.png" />
        <p>
          Elevate your office culture with a share-style meal service that brings your team together over delicious,
          convenient meals. Our tailored office meals program offers a variety of fresh, flavourful dishes that everyone
          can enjoy, fostering collaboration and boosting productivity
        </p>
      </div>
      <div className="authorization-module" style={{ padding: 0 }}>
        {billingDetailsUrl && (
          <>
            <h6 style={{ marginTop: '2rem' }}>Before creating a meal plan, please set up your billing details</h6>
            <a className="button" href={billingDetailsUrl}>
              Set Billing Details
            </a>
          </>
        )}
        {!billingDetailsUrl && <MealPlanForm mealPlan={initialMealPlan} setOpenForm={setIsNew} />}
      </div>
    </div>
  </div>
);

NewMealPlan.propTypes = {
  setIsNew: PropTypes.func.isRequired,
};

export default NewMealPlan;
