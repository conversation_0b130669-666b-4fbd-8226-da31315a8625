import PropTypes from 'prop-types';

// components
import PurchaseOrderDetails from './PurchaseOrderDetails';
import MealPlanPaymentOptions from './MealPlanPaymentOptions';

const MealPlanPaymentDetails = ({ mealPlan, setMealPlan, isNewPlan, setPanel, handleSave, setOpenForm }) => (
  <div
    className="authorization-module plan"
    style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
  >
    {isNewPlan && (
      <a className="meal-plan-back" onClick={() => setPanel('delivery')}>
        Back to Delivery Details
      </a>
    )}
    <PurchaseOrderDetails mealPlan={mealPlan} setMealPlan={setMealPlan} />
    <MealPlanPaymentOptions mealPlan={mealPlan} setMealPlan={setMealPlan} />

    <div className="form-section">
      <a className="button black-btn" onClick={() => handleSave({})} style={{ width: '100%' }}>
        {mealPlan.id ? 'Update Meal Plan' : 'Create Meal Plan'}
      </a>
      <a className="button gray-btn" onClick={() => setOpenForm(false)} style={{ width: '100%' }}>
        Cancel
      </a>
    </div>
  </div>
);

MealPlanPaymentDetails.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  setMealPlan: PropTypes.func.isRequired,
  isNewPlan: PropTypes.bool.isRequired,
  setPanel: PropTypes.func.isRequired,
  handleSave: PropTypes.func.isRequired,
  setOpenForm: PropTypes.func.isRequired,
};
export default MealPlanPaymentDetails;
