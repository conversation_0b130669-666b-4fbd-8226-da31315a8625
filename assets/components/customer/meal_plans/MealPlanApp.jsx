import { useState, useEffect, useContext } from 'react';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewMealPlan from './meal_plan/NewMealPlan';
import MealPlanList from './meal_plan/MealPlanList';
import MealPlanContainer from './MealPlanContainer';
import ClearCartModal from './calendar/ClearCartModal';

const MealPlanApp = () => {
  const [isNew, setIsNew] = useState(false);
  const {
    billingDetailsUrl,
    hasMealPlans: ctxHasMealPlans,
    mealPlanID: mealPlanIDFromContext,
    mealDate,
  } = useContext(appContext);

  const { fetchMealPlans, hasMealPlans, setHasMealPlans, setStartDate } = mealPlansStore(
    (state) => ({
      fetchMealPlans: state.fetchMealPlans,
      mealPlans: state.mealPlans,
      hasMealPlans: state.hasMealPlans,
      setHasMealPlans: state.setHasMealPlans,
      setStartDate: state.setStartDate,
    }),
    shallow
  );

  useEffect(() => setHasMealPlans(ctxHasMealPlans), []);

  useEffect(async () => {
    // set start date from params
    if (mealDate) {
      const mealDateWeek = moment(mealDate).startOf('isoWeek'); // get start of week
      setStartDate(mealDateWeek);
    }

    const mealPlanFilters = JSON.parse(localStorage.getItem('MealPlanFilters') || JSON.stringify({}));
    await fetchMealPlans({
      ...mealPlanFilters,
      ...(mealPlanIDFromContext && { mealPlanID: mealPlanIDFromContext }),
    });
  }, []);

  if (isNew || !hasMealPlans || billingDetailsUrl) {
    return (
      <>
        <NewMealPlan setIsNew={setIsNew} billingDetailsUrl={billingDetailsUrl} />
        <ToastContainer />
      </>
    );
  }

  return (
    <>
      <MealPlanList setIsNew={setIsNew} />
      <MealPlanContainer />
      <ToastContainer />
      <ClearCartModal />
    </>
  );
};

export default MealPlanApp;
