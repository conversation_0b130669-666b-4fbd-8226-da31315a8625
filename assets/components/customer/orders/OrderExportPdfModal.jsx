import { useState } from 'react';
import { exportPdfAPIOrdersPath } from 'routes';

const OrderExportPdfModal = ({ order }) => {
  const [inProgress, setInProgress] = useState({
    'pdf-normal': false,
    'pdf-no-pricing': false,
  });
  const [document, setDocument] = useState(null);

  const generateReport = async ({ variation }) => {
    if (inProgress['pdf-normal'] || inProgress['pdf-no-pricing']) return;

    setDocument(null);
    setInProgress((state) => ({
      ...state,
      [`pdf-${variation}`]: true,
    }));

    const urlParams = `variation=${variation}&order_ids[]=${order.id}`;
    window.location = `${exportPdfAPIOrdersPath({ format: 'pdf' })}?${urlParams}`;

    // eventually return to not in progress
    const waitTime = 3000; // 3 seconds for single order
    setTimeout(() => {
      setInProgress((state) => ({
        ...state,
        [`pdf-${variation}`]: false,
      }));
    }, waitTime);
  };

  return (
    <>
      <h2 className="modal-title text-center">Export Order PDF</h2>
      <div className="export-pdf-options">
        <p className="text-center mb-2">Choose whether to include pricing in the PDF export for Order #{order.id}</p>
        <div className="between-flex">
          <a
            className="button upcase"
            onClick={() => generateReport({ variation: 'normal' })}
            style={{ opacity: inProgress['pdf-normal'] ? 0.6 : 1, fontSize: '14px' }}
          >
            {inProgress['pdf-normal'] ? 'Generating...' : 'With Pricing'}
          </a>
          <a
            className="button hollow upcase"
            onClick={() => generateReport({ variation: 'no-pricing' })}
            style={{ opacity: inProgress['pdf-no-pricing'] ? 0.6 : 1, fontSize: '14px' }}
          >
            {inProgress['pdf-no-pricing'] ? 'Generating...' : 'Without Pricing'}
          </a>
        </div>
        {!!document && (
          <p className="text-center mt-2">
            <a className="button black-btn" href={document.url} target="_blank" rel="noopener noreferrer">
              View Document
            </a>
          </p>
        )}
      </div>
    </>
  );
};

export default OrderExportPdfModal;
