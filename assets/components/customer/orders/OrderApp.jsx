import { useContext, useEffect, useState } from 'react';

// actions
import moment from 'moment';
import appContext from 'contexts/appContext';

// components
import OrderFilters from './OrderFilters';
import OrderList from './OrderList/OrderList';
import OrderCalendar from './OrderCalendar/OrderCalendar';
import { ToastContainer } from 'react-toastify';

const OrderApp = () => {
  const { urlParams, onlyQuotes, defaultOrdersView } = useContext(appContext);

  const { supplier_name, ...sanitizedParams } = urlParams;
  const [isCalendarView, setIsCalendarView] = useState(defaultOrdersView === 'calendar');

  const initDates = {
    from_date: moment().startOf('month').startOf('week').format('YYYY/MM/DD'),
    to_date: moment().endOf('month').endOf('week').format('YYYY/MM/DD'),
  };

  const [params, setParams] = useState({
    ...sanitizedParams,
    // set default date params
    ...initDates,
  });

  useEffect(() => {
    if (!isCalendarView) {
      setParams((state) => ({ ...state, ...initDates }));
    }
  }, [isCalendarView]);

  return (
    <div className="orders">
      {!onlyQuotes && (
        <OrderFilters
          supplierName={supplier_name}
          params={params}
          setParams={setParams}
          isCalendarView={isCalendarView}
          setIsCalendarView={setIsCalendarView}
        />
      )}
      {!isCalendarView && <OrderList params={params} />}
      {isCalendarView && <OrderCalendar params={params} setParams={setParams} />}
      <ToastContainer />
    </div>
  );
};

export default OrderApp;
