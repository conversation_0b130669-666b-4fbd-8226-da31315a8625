import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';

import {
  apiMealPlansPath,
  apiMealPlanPath,
  apiMealPlanOrdersPath,
  apiOrdersPath,
  customerCheckoutDetailsAPICustomersPath,
  apiHolidaysPath,
} from 'routes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import moment from 'moment';

const initialState = {
  loadingMealPlans: true,
  mealPlans: [],
  selectedMealPlan: null,
  orders: [],
  holidays: [],
  fetchedOrders: {},
  fetchedHolidays: {},
  checkoutDetails: {},
  startDate: moment().startOf('isoWeek'),
  hasMealPlans: false,
  clearCartModalOpen: false,
  savedOrderUrl: null,
};

const mealPlansStore = create((set, get) => ({
  ...initialState,
  setStartDate: (date) => {
    set({ startDate: date });
  },
  setHasMealPlans: (hasMealPlans) => {
    set({ hasMealPlans });
  },
  fetchMealPlans: async ({ mealPlanID }) => {
    set({ loadingMealPlans: true });

    try {
      const { data: fetchedMealPlans } = await axios({
        method: 'GET',
        url: apiMealPlansPath({ format: 'json' }),
      });

      set((state) => ({
        mealPlans: [...state.mealPlans, ...fetchedMealPlans],
        loadingMealPlans: false,
      }));

      // set selected Meal Plan (current the first fetched plan) - which also loads the events and holidays
      if (fetchedMealPlans.length) {
        set({ hasMealPlans: true });
        if (mealPlanID && fetchedMealPlans.find((mealPlan) => mealPlan.id === mealPlanID)) {
          get().setSelectedMealPlan(fetchedMealPlans.find((mealPlan) => mealPlan.id === mealPlanID));
        } else {
          get().setSelectedMealPlan(fetchedMealPlans[0]);
        }
      }
    } catch (error) {
      console.error('Something went wrong fetching meal plans:', error);
      handleError(error, 'There was an error fetching meal plans');
      set({ loadingMealPlans: false });
    }
  },
  fetchEvents: async ({ from_date, mealPlanID, forOrders = false, initialLoad = false }) => {
    if (initialLoad) {
      set(() => ({ fetchedOrders: {}, orders: [] }));
    }

    const startDate = moment(from_date);
    let endDate = moment(from_date).add(6, 'days');
    if (initialLoad) {
      endDate = moment(from_date).add(3, 'weeks').endOf('isoWeek');
    }

    const weeksInRange = [];
    const currentDate = startDate.clone();

    while (currentDate.isBefore(endDate, 'week') || currentDate.isSame(endDate, 'week')) {
      weeksInRange.push(currentDate.format('YYYY-MM-DD'));
      currentDate.add(1, 'week');
    }

    const { fetchedOrders, fetchedHolidays } = get();
    const alreadyFetched = weeksInRange.every((week) => fetchedOrders[week]);
    const alreadyFetchedHolidays = weeksInRange.every((week) => fetchedHolidays[week]);

    if (alreadyFetched) {
      // no need to refetch
      return;
    }

    try {
      let newOrders = [];
      if (forOrders || mealPlanID) {
        const fetchURL = mealPlanID ? apiMealPlanOrdersPath(mealPlanID, { format: 'json' }) : apiOrdersPath({ format: 'json' })
        const { data: fetchedOrders } = await axios({
          method: 'GET',
          params: {
            from_date: startDate.format('YYYY-MM-DD'),
            to_date: endDate.format('YYYY-MM-DD'),
          },
          url: fetchURL,
        });
        newOrders = fetchedOrders;
      }

      let newHolidays = [];
      if (!alreadyFetchedHolidays) {
        const { data } = await axios({
          method: 'GET',
          params: {
            from_date: startDate.format('YYYY-MM-DD'),
            to_date: endDate.format('YYYY-MM-DD'),
          },
          url: apiHolidaysPath({ format: 'json' }),
        });
        newHolidays = data;
      }

      set((state) => ({
        orders: [...state.orders, ...newOrders],
        holidays: [...state.holidays, ...newHolidays],
        fetchedOrders: weeksInRange.reduce(
          (cache, week) => {
            cache[week] = true;
            return cache;
          },
          { ...state.fetchedOrders }
        ),
        fetchedHolidays: weeksInRange.reduce(
          (cache, week) => {
            cache[week] = true;
            return cache;
          },
          { ...state.fetchedHolidays }
        ),
      }));
    } catch (error) {
      console.error('Something went wrong fetching week events:', error);
      handleError(error, 'There was an error fetching week events');
    }
  },
  setSelectedMealPlan: async (mealPlan) => {
    set({
      selectedMealPlan: mealPlan,
    });

    setLocalStorage({ mealPlanID: mealPlan.id });

    await get().fetchEvents({
      mealPlanID: mealPlan.id,
      from_date: get().startDate,
      initialLoad: true,
    });
  },
  createMealPlan: async ({ mealPlan }) => {
    try {
      const { data: createdMealPlan } = await axios({
        method: 'POST',
        url: apiMealPlansPath({ format: 'json' }),
        data: { meal_plan: mealPlan },
        headers: csrfHeaders(),
      });

      set((state) => ({
        mealPlans: [...state.mealPlans, createdMealPlan],
        hasMealPlans: true,
      }));
      await get().setSelectedMealPlan(createdMealPlan);
      toast.success(`Created new meal plan with name '${createdMealPlan.name}'`, {
        ...defaultToastOptions,
        autoClose: 5000,
      });
    } catch (error) {
      handleError(error, 'There was an error creating a new Meal Plan');
      return Promise.reject(error);
    }
  },
  updateMealPlan: async ({ mealPlan, mode, orderIDs }) => {
    try {
      const { data: updatedMealPlan } = await axios({
        method: 'PUT',
        url: apiMealPlanPath(mealPlan, { format: 'json' }),
        data: {
          meal_plan: mealPlan,
          mode,
          ...(orderIDs && orderIDs.length && { order_ids: orderIDs }),
        },
        headers: csrfHeaders(),
      });

      set((state) => ({
        mealPlans: state.mealPlans.map((stateMealPlan) => {
          if (stateMealPlan.id == updatedMealPlan.id) {
            return updatedMealPlan;
          }
          return stateMealPlan;
        }),
        selectedMealPlan: updatedMealPlan,
      }));
      toast.success(`Updated meal plan with name '${updatedMealPlan.name}'`, {
        ...defaultToastOptions,
        autoClose: 5000,
      });
      if (mode === 'subsequent') {
        toast.info(`Order updates will be reflected shotly!`, {
          ...defaultToastOptions,
          autoClose: 5500,
        });
      }
    } catch (error) {
      if (!error?.response?.data?.pending_orders?.length) {
        handleError(error, `There was an error updating the Meal Plan named ${mealPlan.name}`);
      }
      return Promise.reject(error);
    }
  },
  removeMealPlan: async ({ mealPlan, mode, orderIDs }) => {
    try {
      const { data: removedMealPlan } = await axios({
        method: 'DELETE',
        url: apiMealPlanPath(mealPlan, { format: 'json' }),
        data: {
          mode,
          ...(orderIDs && orderIDs.length && { order_ids: orderIDs }),
        },
        headers: csrfHeaders(),
      });

      const newSelectedMealPlan = get().mealPlans.filter((savedMealPlan) => savedMealPlan.id !== removedMealPlan.id)[0];
      if (newSelectedMealPlan) {
        get().setSelectedMealPlan(newSelectedMealPlan);
      } else {
        set({ hasMealPlans: false });
      }
      set((state) => ({
        mealPlans: state.mealPlans.filter((stateMealPlan) => stateMealPlan.id !== removedMealPlan.id),
      }));
      toast.success(`Meal plan with name '${removedMealPlan.name}' is now Archived!`, {
        ...defaultToastOptions,
        autoClose: 5000,
      });
      if (mode === 'subsequent') {
        toast.info(`Order updates will be reflected shotly!`, {
          ...defaultToastOptions,
          autoClose: 5500,
        });
      }
    } catch (error) {
      if (!error?.response?.data?.pending_orders?.length) {
        handleError(error, `There was an error removing the Meal Plan named ${mealPlan.name}`);
      }
      return Promise.reject(error);
    }
  },
  fetchCheckoutDetails: async () => {
    const { data: fetchedCheckoutDetails } = await axios({
      method: 'GET',
      url: customerCheckoutDetailsAPICustomersPath({ format: 'json' }),
    });

    set((state) => ({
      checkoutDetails: fetchedCheckoutDetails,
    }));
  },
  addSavedCard: (creditCard) => {
    set((state) => ({
      checkoutDetails: {
        ...state.checkoutDetails,
        saved_credit_cards: [creditCard, ...state.checkoutDetails.saved_credit_cards],
      },
    }));
  },
  setClearCartModalOpen: (open) => {
    set((state) => ({ clearCartModalOpen: open }));
  },
  setSavedOrderUrl: (savedOrderUrl) => {
    set((state) => ({ savedOrderUrl }));
  },
  changeOrderStatus: async ({ apiEndpoint, reactivate }) => {
    const method = reactivate ? 'put' : 'delete';

    try {
      const { data: updatedOrder } = await axios(apiEndpoint, { method, headers: csrfHeaders() });

      set((state) => ({
        orders: state.orders.map((order) => (order.id === updatedOrder.id ? updatedOrder : order)),
      }));
    } catch (error) {
      console.error('Error changing order status:', error);
      handleError(error, 'There was an error updating the order status');
    }
  },
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status) && error?.response?.data?.errors) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

function setLocalStorage({ ...filters }) {
  const storageKey = 'MealPlanFilters';
  const mealPlanFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));

  const updatedFilter = { ...mealPlanFilters, ...filters };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default mealPlansStore;
