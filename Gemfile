source 'https://rubygems.org'
ruby '2.7.1'

# Application level gems
gem 'bootsnap'
gem 'get_process_mem', '~> 1.0.0' # gets server memory usage # only used in Woolworths Import Products
gem 'pg' # database engine in used for production - see config/databases.yml also
gem 'puma'
gem 'rack-attack'
gem 'rack-cors' # for setting up CORS with gatsby and nextJS app
gem 'rails', '~> 6.1', '>= 6.1.7.8' # need to upgrade
# gem 'therubyracer', platforms: :ruby # See https://github.com/sstephenson/execjs#readme # required for Heroku

# Testing
group :development, :test do
  gem 'byebug' # Allow debugging in development and test
  gem 'dotenv-rails' # required for ENV variables
  gem 'rails-controller-testing'
  gem 'rspec-rails'
end

group :development, :test, :staging do
  gem 'factory_bot', '6.4.2'
  gem 'factory_bot_rails', '6.4.2'
  gem 'faker' # helps create dummy data
end

group :development do
  gem 'guard-rspec', require: false
  gem 'rubocop', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
end

group :test do
  gem 'capybara', '~> 2.18.0'
  gem 'database_cleaner-active_record', '>= 2.0'
  gem 'poltergeist', '~> 1.18.1'
  gem 'shoulda-matchers', '~> 4.0.1' # allows is_expected.to specs
  gem 'simplecov'
end

# Debugging / Error Reporting
gem 'awesome_print'
gem 'newrelic_rpm'
gem 'sentry-raven'
gem 'slack-notifier'

# User Allocation and Permissions
gem 'devise'
gem 'doorkeeper', '~> 5.5'
gem 'hubspot-ruby'

# Database related
gem 'active_record_union' # only used in app/models/supplier_profile.rb scope :search_by_keywords
gem 'delayed_job_active_record' # For delaying the process of updating subsequent orders # can move to Sidekiq
gem 'haversine' # for geo radius , used for delivery zone and suburbs and suppliers list
gem 'paper_trail' # , '~> 3.0.5' # for tracing order changes for rails 3 # data record versions
gem 'pg_search' # data related postgres search
gem 'sendgrid-ruby'
gem 'shortener' # For generating short URLS to use in emails # only used for broken order confirm / reject links

group :development do
  gem 'annotate' # need to re-run annotate
  gem 'bullet', '~> 6.1.5' # show N+1 queries
end

# Payment
gem 'activemerchant' # for e-way payments
gem 'stripe', '~> 4.24.0', require: 'stripe'

# JS related gems
gem 'highcharts-rails' # The gem version mirrors the included version of Highcharts # used in customer profiles reports
gem 'jquery-fileupload-rails'
gem 'jquery-rails'
gem 'jquery-timepicker-addon-rails' # datetimepicker
gem 'jquery-ui-rails'
gem 'js_cookie_rails' # for cookies in javascript
# gem 'paperclip' # used by rich gem # deprecated on github
gem 'js-routes'
gem 'select2-rails' # Integrates the Select2 jQuery plugin with the Rails asset pipeline.
gem 'tinymce-rails', '~> ********'
gem 'webpacker', '~> 5.4.2'

# View related gems
gem 'geocoder', '~> 1.5' # to show google maps for orders
gem 'haml', '~> 5.1'
gem 'haml-rails' # Include haml to make building sitemaps easier (old comment)
gem 'jbuilder' # For building json templates
gem 'kaminari' # for pagination
gem 'recaptcha', '~> 5.16' # used in registration form
gem 'simple_form' # forms

# View processing related
gem 'sass-rails', '~> 5.0' # Use SCSS for stylesheets
gem 'sprockets', '3.7.2' # required for Heroku?
gem 'uglifier', '>= 1.0.3'

# Document related gems
gem 'carrierwave' # for uploading pictures # also required by rich gem
gem 'cloudinary', '1.18.1' # for file uploading
gem 'docusign_esign', '1.0.0' # esign the pdf documents
gem 'prawn', '2.4.0' # PDF Generator
gem 'prawn-svg', '~> 0.32.0' # Add svgs to PDFs
gem 'prawn-table', '~> 0.2.2' # Add tables to PDFs
gem 'rqrcode', '~> 2.0' # generate QR codes to insert in to PDFs
gem 'xeroizer', '~> 3.0.1' # for Xero # OAuth1.0a API interface with Xero for Invoices and Purchases

# Not used gems but require code cleanup
# getting Heroku Sprockets::NotImplementedError: Custom asset_path helper is not implemented
# http://stackoverflow.com/questions/36947064/sprockets-error-sprocketsnotimplementederror-custom-asset-path-helper-is-not
gem 'sprockets-helpers' # Not actually used but will need testing for Heroku

group :production, :staging, :test do
  gem 'redis', '~> 4.8.1'
end

group :production, :staging do
  gem 'remote_syslog_logger'
end
