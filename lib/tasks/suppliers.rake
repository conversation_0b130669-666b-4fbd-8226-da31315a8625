namespace :suppliers do

  # Push purchases for selected suppliers delivered in the last week
  # Task to run every monday
  desc 'Generate Supplier Invoices - RGIs'
  task :generate_supplier_invoices, %i[frequency forced] => :environment do |_, args|
    now = Time.zone.now
    is_forced = args[:forced].present?

    # only runs at 10am
    if now.to_s(:hour_only) != '10' && !is_forced
      next
    end

    possible_frequencies = %w[weekly fortnightly monthly]
    frequencies = case
    when args[:frequency].present?
      possible_frequencies & [args[:frequency]]
    else
      possible_frequencies
    end

    frequencies.each do |frequency|
      can_process = case frequency
      when 'weekly'
        [now.beginning_of_week, now.beginning_of_month].include?(now.beginning_of_day)
      when 'fortnightly'
        now.beginning_of_day == now.beginning_of_week
      when 'monthly'
        now.beginning_of_day == now.beginning_of_month
      else
        false
      end

      if !(is_forced || can_process)
        next
      end

      puts "=========== Starting suppliers.generate_supplier_invoices - #{frequency.upcase} at #{Time.zone.now} ==========="
      invoices_generator = Suppliers::GenerateOrderInvoices.new(frequency: frequency, time: now, notify_supplier: true).call
      if !invoices_generator.success?
        Raven.capture_exception(RakeTaskError.new('There was an error when pushing purchases orders to xero'),
          extra: { errors: invoices_generator.errors, frequency: frequency, time: now },
          transaction: 'rake suppliers.generate_supplier_invoices'
        )
      end
      puts "=========== Completed suppliers.generate_supplier_invoices - #{frequency.upcase} at #{Time.zone.now} ==========="
    end
  end

  desc 'Cache All Suppliers Category Groups based on active menu sections'
  task :cache_category_groups, [:supplier_id] => :environment do |_, args|
    puts "=========== Starting suppliers:cache_category_groups at #{Time.zone.now} ==========="

    suppliers = args[:supplier_id].present? ? SupplierProfile.where(id: args[:supplier_id]) : SupplierProfile.where(is_searchable: true)

    begin
      Suppliers::Cache::CategoryGroups.new(suppliers: suppliers, verbose: true).call
    rescue => exception
      Raven.capture_exception(RakeTaskError.new('There was an error when saving supplier category groups'),
        extra: { errors: exception.backtrace.join('\n') },
        transaction: 'rake suppliers:cache_category_groups'
      )
    end
    puts "=========== Completed suppliers:cache_category_groups at #{Time.zone.now} ==========="
  end

  desc 'Cache All Dietary Preference for the supplier based on menu items'
  task :cache_dietary_preferences, [:preference] => :environment do |_, args|
    puts "=========== Starting suppliers.cache_dietary_preferences at #{Time.zone.now} ==========="
    supplier_preferences = case
    when args[:preference].present?
      [args[:preference]]
    else
      Suppliers::Cache::DietaryPreference::POSSIBLE_PREFERENCES
    end

    dietary_preference_errors = []
    supplier_preferences.each do |preference|
      preference_setter = Suppliers::Cache::DietaryPreference.new(preference: preference).call
      if preference_setter.success?
        puts "#{preference} => #{preference_setter.preference_suppliers.size}"
      else
        dietary_preference_errors += preference_setter.errors
      end
    end
    if dietary_preference_errors.present?
      Raven.capture_exception(RakeTaskError.new('There was an error when saving supplier dietary preferences'),
        extra: { errors: dietary_preference_errors },
        transaction: 'rake suppliers:cache_dietary_preferences'
      )
    end
    puts "=========== Completed suppliers.cache_dietary_preferences at #{Time.zone.now} ==========="
  end

  desc 'Cache delivery details like delivery fee, operating days and hours'
  task cache_delivery_details: :environment do
    puts "=========== Starting suppliers.cache_delivery_details at #{Time.zone.now} ==========="
    suppliers_with_delivery_zones = SupplierProfile.where(is_searchable: true).joins(:delivery_zones).order(:company_name).distinct

    same_fee_suppliers = []
    same_day_suppliers = []
    same_hour_suppliers = []
    suppliers_with_delivery_zones.each do |supplier|
      details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier, verbose: true).call
      same_fee_suppliers << supplier if details_saver.has_same_delivery_fee
      same_day_suppliers << supplier if details_saver.has_same_operating_days
      same_hour_suppliers << supplier if details_saver.has_same_operating_hours
    end

    puts ''
    puts "Total suppliers: #{suppliers_with_delivery_zones.size}"
    puts "same_fee suppliers: #{same_fee_suppliers.size}"
    puts "same_days suppliers: #{same_day_suppliers.size}"
    puts "same_hours suppliers: #{same_hour_suppliers.size}"
    puts ''
    SupplierProfile.where.not(delivery_fee: nil).where.not(id: same_fee_suppliers.map(&:id)).each do |supplier|
      supplier.update(delivery_fee: nil)
      print 'dfx-'
    end
    SupplierProfile.where.not(operating_days: nil).where.not(id: same_day_suppliers.map(&:id)).each do |supplier|
      supplier.update(operating_days: nil)
      print 'odx-'
    end
    SupplierProfile.where.not(operating_hours: nil).where.not(id: same_hour_suppliers.map(&:id)).each do |supplier|
      supplier.update(operating_hours: nil)
      print 'ohx-'
    end
    puts "=========== Completed suppliers.cache_delivery_details at #{Time.zone.now} ==========="
  end

  desc 'Set supplier Closure Dates'
  task :set_closure_dates, %i[close_from close_to] => :environment do |_, args|
    close_from_date = args[:close_from].presence || yordar_credentials(:yordar, :closure_start_date)
    close_from = Time.zone.parse(close_from_date).beginning_of_day

    close_to_date = args[:close_to].presence || yordar_credentials(:yordar, :closure_end_date)
    close_to = Time.zone.parse(close_to_date).end_of_day

    puts "Setting supplier closure dates from #{close_from} - #{close_to}"

    SupplierProfile.all.each do |supplier|
      if supplier.update(close_from: close_from, close_to: close_to)
        print 's-'
      else
        print 'se-'
      end
    end
  end

  desc 'Manually sync supplier record(s) to Hubspot'
  task :sync_suppliers_to_hubspot, [:supplier_id] => :environment do |_, args|
    if args[:supplier_id].present?
      suppliers = SupplierProfile.where(id: args[:supplier_id]).includes(:user)
    else
      suppliers = SupplierProfile.where(is_searchable: true).includes(:user)
    end

    puts "Syncing #{suppliers.size} suppliers!"

    suppliers.each do |supplier|
      puts ''
      puts ''
      print "Supplier #{supplier.name} - #{supplier.company_address_suburb&.label}"
      syncer = Hubspot::SyncContact.new(contact: supplier.user, refresh: true).call
      if syncer.success?
        print ' - Success'
      else
        print " - UnSuccessful - #{syncer.errors.join(' | ')}"
      end
    end
  end

  desc 'Save Delivery Zone suburbs'
  task :save_deliverable_suburbs, %i[supplier_id delivery_zone_id verbose] => :environment do |_, args|
    start_time = Time.zone.now.dup
    puts "=========== Starting suppliers.save_deliverable_suburbs at #{start_time} ==========="
    supplier = args[:supplier_id].present? ? SupplierProfile.where(id: args[:supplier_id]).first : nil
    supplier_delivery_zone = supplier.present? && args[:delivery_zone_id].present? ? supplier.delivery_zones.where(id: args[:delivery_zone_id]).first : nil
    is_verbose = args[:verbose].present?

    delivery_zones = DeliveryZone.joins(:supplier_profile).where(supplier_profiles: { is_searchable: true }).order(radius: :desc).includes(:suburb)
    delivery_zones = delivery_zones.where(supplier_profile: supplier) if supplier.present?
    delivery_zones = delivery_zones.where(id: supplier_delivery_zone.id) if supplier_delivery_zone.present?

    suburb_grouped_delivery_zones = delivery_zones.group_by{|delivery_zone| [delivery_zone.suburb, delivery_zone.radius] }
    sorted_grouped_delivery_zones = suburb_grouped_delivery_zones.sort_by{|_, suburb_delivery_zones| -suburb_delivery_zones.size }
    if is_verbose
      puts ''
      puts "Total delivery zones: #{sorted_grouped_delivery_zones.size}"
    end
    sorted_grouped_delivery_zones.each_with_index do |(suburb_radius, suburb_delivery_zones), idx|
      if is_verbose
        puts ''
        puts "#{idx} - #{suburb_radius[0].label} with #{suburb_radius[1]}km radius => #{suburb_delivery_zones.size}"
      end
      deliverable_suburbs = []
      suburb_delivery_zones.each do |delivery_zone|
        deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true, cached_deliverable_suburbs: deliverable_suburbs).call
        puts "Deliverable Suburbs: #{deliverable_suburbs.size}" if is_verbose
      end
    end

    sync_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting suppliers.save_deliverable_suburbs at #{Time.zone.now} - Duration: #{sync_duration} mins ==========="
  end

  task :supplier_recurring_order_reminder, %i[time verbose] => :environment do |_, args|
    now = Time.zone.now.dup
    is_verbose = args[:verbose].present?
    notification_time = args[:time].present? ? Time.zone.parse(args[:time]) : now
    puts "=========== Starting suppliers:notify_supplier_before_cutoff at #{now} ==========="
    orders_reminder = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time, verbose: is_verbose).call

    puts "Notified #{orders_reminder.notified_suppliers.uniq.size} suppliers for #{orders_reminder.notified_orders.uniq.size} orders"

    sync_duration = ((Time.zone.now - now) / 1.minute).round(2)
    puts "=========== Completed suppliers:notify_supplier_before_cutoff at #{Time.zone.now} - Duration #{sync_duration} mins ==========="
  end

  desc 'Send Menu Reminders - Menu not updated in the last x months based on menu_reminder_frequency'
  task :supplier_menu_reminders, %i[time] => :environment do |_, args|
    now = Time.zone.now.dup
    notification_time = args[:time].present? ? Time.zone.parse(args[:time]) : now
    puts "=========== Starting suppliers:supplier_menu_reminders at #{now} ==========="
    notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: notification_time).call

    puts "Notified #{notified_suppliers.size} suppliers about their menu"
    puts "=========== Completed suppliers:supplier_menu_reminders at #{Time.zone.now} ==========="
  end

end
